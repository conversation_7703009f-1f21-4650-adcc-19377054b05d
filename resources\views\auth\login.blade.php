<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, shrink-to-fit=no">
    <title>{{ config('app.name') }} | login </title>
    <link rel="icon" type="image/x-icon" href="{{ asset('static/src/assets/img/logo-vcheck.png') }}" />
    <link href="{{ asset('static/layouts/vertical-light-menu/css/light/loader.css') }}" rel="stylesheet" type="text/css" />
    <script src="{{ asset('static/layouts/vertical-light-menu/loader.js') }}"></script>
    <!-- BEGIN GLOBAL MANDATORY STYLES -->
    <link href="https://fonts.googleapis.com/css?family=Nunito:400,600,700" rel="stylesheet">
    <link href="{{ asset('static/src/bootstrap/css/bootstrap.min.css') }}" rel="stylesheet" type="text/css" />

    <link href="{{ asset('static/layouts/vertical-light-menu/css/light/plugins.css') }}" rel="stylesheet" type="text/css" />
    <link href="{{ asset('static/src/assets/css/light/authentication/auth-cover.css') }}" rel="stylesheet" type="text/css" />

    <!-- END GLOBAL MANDATORY STYLES -->

</head>
<body class="form">

    <div class="auth-container d-flex">

        <div class="container mx-auto align-self-center">

            <div class="row">

                <div class="col-6 d-lg-flex d-none h-100 my-auto top-0 start-0 text-center justify-content-center flex-column">
                    <div class="auth-cover-bg-image"></div>
                    <div class="auth-overlay"></div>
                    <div class="auth-cover">
                        <div class="position-relative">
                            <img src="{{ asset('static/src/assets/img/logo-vcheck.png') }}" alt="auth-img">
                            <h2 class="text-black font-weight-bolder px-2"> Gestion et Suivi des flottes de véhicule </h2>
                            <p class="text-black px-2">Gérer facilement vos flottes de véhicule avec cette plateforme</p>
                        </div>

                    </div>

                </div>

                <div class="col-xxl-4 col-xl-5 col-lg-5 col-md-8 col-12 d-flex flex-column align-self-center ms-lg-auto me-lg-0 mx-auto">
                    <div class="card">
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-12 mb-3">
                                    <img src="{{ asset('static/src/assets/img/logo-vcheck.png') }}" class="mb-4 w-50" alt="auth-img">
                                    <h2>CONNEXION</h2>
                                    <b>Entrer votre adresse email et votre mot de passe pour vous connecter à votre compte</b>
                                </div>
                                <form action="{{ route('def_log_user') }}" method="POST" enctype="multipart/form-data">
                                    @csrf
                                    <div class="col-md-12">
                                        <div class="mb-3">
                                            <label class="form-label">Adresse Email</label>
                                            <input type="email" class="form-control" name="email" onpaste="return false;" oncopy="return false;" oncut="return false;" placeholder="<EMAIL>" value="{{ old('email') }}" required>
                                            @error('email') <span class="text-danger error">Veuillez saisir une bonne adresse email valide</span>@enderror
                                        </div>
                                    </div>
                                    <div class="col-12">
                                        <div class="mb-4">
                                            <label class="form-label">Mot de passe</label>
                                            <input type="password" class="form-control" name="password" onpaste="return false;" oncopy="return false;" oncut="return false;" required>
                                            <small></small>
                                            @error('password') <span class="text-danger error">Veuillez bien verifier votre mot de passe</span>@enderror
                                        </div>

                                    </div>
                                    <div class="col-12">
                                        <div class="mb-3">
                                            <div class="form-check form-check-primary form-check-inline">
                                                <input class="form-check-input me-3" type="checkbox" name="remember" id="remember" value="1" {{ old('remember') ? 'checked' : '' }}>
                                                <label class="form-check-label" for="remember">
                                                    Se souvenir de moi
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-12">
                                        <div class="mb-4">
                                            <button type="submit" class="btn btn-secondary w-100 text-uppercase">Se connecter</button>
                                        </div>
                                    </div>
                                </form>
                            </div>

                        </div>
                    </div>
                </div>

            </div>

        </div>

    </div>

    <script src="https://code.jquery.com/jquery-3.7.1.min.js" integrity="sha256-/JqT3SQfawRcv/BIHPThkBvs0OEvtFFmqPF/lYI/Cxo=" crossorigin="anonymous"></script>
    @include('flashy::message')
    <!-- BEGIN GLOBAL MANDATORY SCRIPTS -->
    <script src="{{ asset('static/src/bootstrap/js/bootstrap.bundle.min.js') }}"></script>
    <!-- END GLOBAL MANDATORY SCRIPTS -->


</body>
</html>
