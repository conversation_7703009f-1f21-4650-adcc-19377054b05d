@extends('layouts.base')
@section('content')
<x-breadcrumb :current="'Modification agent'" previous="Agent" :previousRoute="route('agent.show', $agent->id)" />
<div class="layout-px-spacing">
    <div class="middle-content container-xxl p-0">
        <div class="row">
            <div id="flStackForm" class="col-lg-12 layout-spacing layout-top-spacing">
                <div class="statbox widget box box-shadow">
                    <div class="widget-header">
                        <div class="row">
                            <div class="col-xl-12 col-md-12 col-sm-12 col-12">
                                <h4>Modification du compte de l'agent: <b>{{ $agent->PrenomAgent }} {{ $agent->NomAgent }}</b></h4>
                            </div>
                        </div>
                    </div>
                    <div class="widget-content widget-content-area">
                        <form id="FormValidationAgent" action="{{ route('agent.update', $agent->id)}}" method="POST" enctype="multipart/form-data" onsubmit="return validerNumero()">
                            @csrf
                            @method('PUT')
                            <div class="mb-3 row">
                                <label for="CodeAgent" class="col-sm-2 col-form-label">Code Agent *</label>
                                <div class="col-sm-10">
                                    <input type="text" class="form-control text-black" id="CodeAgent" name="CodeAgent" value="{{ $agent->CodeAgent }}" readonly>
                                </div>
                            </div>
                            <div class="mb-3 row">
                                <label for="NomAgent" class="col-sm-2 col-form-label">Nom Agent *</label>
                                <div class="col-sm-10">
                                    <input type="text" class="form-control" id="NomAgent" name="NomAgent" value="{{ $agent->NomAgent }}">
                                </div>
                            </div>

                            <div class="mb-3 row">
                                <label for="PrenomAgent" class="col-sm-2 col-form-label">Prenom Agent *</label>
                                <div class="col-sm-10">
                                    <input type="text" class="form-control" id="PrenomAgent" name="PrenomAgent" value="{{ $agent->PrenomAgent }}">
                                </div>
                            </div>

                            <div class="mb-3 row">
                                <label for="Sexe" class="col-sm-2 col-form-label">Sexe *</label>
                                <div class="col-sm-10">
                                    <select name="Sexe" id="Sexe" class="form-control">
                                        <option value="M">Masculin</option>
                                        <option value="F">Feminin</option>
                                    </select>
                                </div>
                            </div>

                            <div class="mb-3 row">
                                <label for="Telephone" class="col-sm-2 col-form-label">Telephone *</label>
                                <div class="col-sm-10">
                                    <input type="text" class="form-control" id="Telephone" name="Telephone" value="{{ $agent->Telephone }}">
                                </div>
                            </div>

                            <div class="mb-3 row">
                                <label for="Photo" class="col-sm-2 col-form-label">Photo *</label>
                                <div class="col-sm-10">
                                    <!-- Aperçu de la photo actuelle -->
                                    <img id="preview" src="{{ $agent->Photo }}" alt="Agent photo" style="max-width: 200px; max-height: 200px; display: block; margin-bottom: 10px;">

                                    <!-- Champ caché + bouton déclencheur -->
                                    <input type="file" class="form-control d-none" id="Photo" name="Photo" accept="image/*">
                                    <button type="button" class="btn btn-outline-primary" id="btnEditPhoto">Modifier la photo</button>
                                </div>
                            </div>

                            <div class="mb-3 row">
                                <label for="Pseudo" class="col-sm-2 col-form-label">Pseudo * (<small>Identifiant pour vous connecter</small>)</label>
                                <div class="col-sm-10">
                                    <input type="text" class="form-control" id="Pseudo" name="Pseudo" value="{{ $agent->Pseudo}}">
                                </div>
                            </div>

                            <div class="form-group row">
                                <div class="col-sm-10 offset-sm-2 mt-3">
                                    <a href="{{ URL::previous() }}" class="btn btn-danger">Annuler</a>
                                    <button type="submit" class="btn btn-dark">Modifier l'agent</button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>

        </div>
    </div>
</div>
@endsection
@push('scripts')
<script src="{{asset('static/src/assets/js/jsPage/form-create-agent.js') }}"></script>
@endpush
