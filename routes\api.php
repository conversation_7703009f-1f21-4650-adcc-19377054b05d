<?php

use App\Http\Controllers\Api\AgentController;
use App\Http\Controllers\Api\ScanController;
use App\Http\Controllers\Api\VehiculeApiController;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "api" middleware group. Make something great!
|
*/

Route::post('/loginAgent', [AgentController::class, 'loginAgent']); //Connexion agent
Route::middleware('auth:sanctum')->group(function () {
    Route::get('/vehicules', [VehiculeApiController::class, 'vehiculeIndex']); //Vehicule
    Route::post('/logoutAgent', [AgentController::class, 'logoutAgent']); //Deconnexion agent
    Route::get('/agentAuthData', [AgentController::class, 'getAgentAuth']); //Recuperation de toutes les informations relatives à l'agent connecté
    Route::get('/agent/photo', [AgentController::class, 'userPhoto']); //Recuperation de la photo de l'agent connecté
    Route::post('/scanVehicule/{qrCode}', [ScanController::class, 'scanDecryptQrCodeCarte']); //Decodage du QR Code de la carte
    Route::get('/scanPorte/{qrCode}', [ScanController::class, 'scanDecryptQrCodePorte']); //Decodage du QR Code de la porte
    Route::post('/scanQrCodeStore', [ScanController::class, 'scanQrCodeStore']); //Enregistrement d'un scan
});

Route::get('/agents', [AgentController::class, 'agentAll']);
