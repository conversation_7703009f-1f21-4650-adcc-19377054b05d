<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Laravel\Sanctum\HasApiTokens;

class Scan extends Model
{
    use HasFactory, HasApiTokens;

    protected $fillable = [
        'porte_id',
        'agent_id',
        'carte_id',
        'Direction',
        'DateHeure'
    ];

    public function porte()
    {
        return $this->belongsTo(Porte::class);
    }
    
    public function agent()
    {
        return $this->belongsTo(Agent::class);
    }

    public function carte(){
        return $this->belongsTo(Carte::class);
    }
}
