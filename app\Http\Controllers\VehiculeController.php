<?php

namespace App\Http\Controllers;

use App\Models\Beneficiaire;
use App\Models\MarqueVoiture;
use App\Models\TypeVehicule;
use App\Models\Vehicule;
use Illuminate\Http\Request;
use MercurySeries\Flashy\Flashy;

class VehiculeController extends Controller
{
    //Fonction qui permet de renvoyé le formulaire de modification d'un vehicule
    public function editVehicule($idVehicule, $idBeneficiaire)
    {
        $vehicule = Vehicule::find($idVehicule);
        $beneficiaire = Beneficiaire::find($idBeneficiaire);
        $marques = MarqueVoiture::all();
        $typeVehicules = TypeVehicule::all();
        return view('vehicules.edit', compact('vehicule', 'beneficiaire', 'marques', 'typeVehicules'));
    }

    // Fonction de mise à jour d'un vehicule
    function updateVehicule(Request $request, $idVehicule, $idBeneficiaire)
    {
        // Récupérez l'article à mettre à jour
        $vehicule = Vehicule::findOrFail($idVehicule);

        // Récup<PERSON>rez le beneficiaire du vehicule
        $beneficiaire = Beneficiaire::findOrFail($idBeneficiaire);

        // Mettez à jour les données de l'article avec les données du formulaire
        $vehicule->update([
            'Immatriculation' => preg_replace('/\s+/', '', strtoupper($request->Immatriculation)),
            'marque_voiture_id' => $request->input('marque_id'),
            'Model' => $request->input('Model'),
            'type_vehicule_id' => $request->input('type_vehicule_id'),
            'Couleur' => $request->input('Couleur'),
            'Description' => $request->input('Description'),
            'beneficiaire_id' => $beneficiaire->id,
            'info_pdf' => isset($request->info_pdf) ? $request->info_pdf = upload($request->info_pdf, 'doc') : null,

        ]);
        Flashy::success("Véhicule modifié avec succès");
        return redirect()->route('beneficiaire.show', $beneficiaire->id);
    }

    public function store(Request $request)
    {
        $immatriculationExistante = Vehicule::where('Immatriculation', $request->input('Immatriculation'))->exists();
        if ($immatriculationExistante) {
            // Si l'immatriculation existe déjà, afficher un message d'erreur avec Flashy
            Flashy::error("Immatriculation existant, veillez saisir un autre");
            return redirect()->back();
        }

        Vehicule::create([
            'Immatriculation' => preg_replace('/\s+/', '', strtoupper($request->Immatriculation)),
            'Model' => $request->Model,
            'Couleur' => $request->Couleur,
            'Description' => $request->Description,
            'marque_voiture_id' => $request->marque_voiture_id, //Cette valeur est à changé (normalement la valeur doit provenir de la request)
            'type_vehicule_id' => $request->type_vehicule_id,
            'beneficiaire_id' => $request->beneficiaire_id,
            'Statut' => 0,
            'info_pdf' => isset($request->info_pdf) ? $request->info_pdf = upload($request->info_pdf, 'doc') : null,

        ]);

        Flashy::primary("Véhicule ajouté avec succès");
        $nouveauBeneficiaireId = $request->beneficiaire_id;
        return redirect()->route('beneficiaire.show', $nouveauBeneficiaireId);
    }

    function change_vehicule_state($id)
    {
        $vehicule = Vehicule::find($id);
        if ($vehicule->Statut == 1) {
            # change state to 0
            vehicule::where('id', $id)->update(array('Statut' => 0));
            Flashy::warning("Statut véhicule désactivé avec succès");
        } else {
            // change state to 1
            vehicule::where('id', $id)->update(array('Statut' => 1));
            Flashy::success("Statut véhicule activé avec succès");
        }
        return redirect()->back();
    }

    public function destroy(string $id, Request $request)
    {
        Vehicule::where('id', $id)->where('beneficiaire_id', $request->beneficiaire_id)->delete();
        Flashy::warning("Véhicule supprimé avec succès");
        $nouveauBeneficiaireId = $request->beneficiaire_id;
        return redirect()->route('beneficiaire.show', $nouveauBeneficiaireId);
    }
}
