<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('scans', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('porte_id')->index();
            $table->foreign('porte_id')->references('id')->on('portes')->onDelete('cascade')->onUpdate('cascade');

            $table->unsignedBigInteger('agent_id')->index();
            $table->foreign('agent_id')->references('id')->on('agents')->onDelete('cascade')->onUpdate('cascade');

            $table->unsignedBigInteger('carte_id')->index();
            $table->foreign('carte_id')->references('id')->on('cartes')->onDelete('cascade')->onUpdate('cascade');

            $table->string('Direction');
            $table->dateTime('DateHeure');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('scans');
    }
};
