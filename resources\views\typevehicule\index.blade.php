@extends('layouts.base')
@section('content')
<div class="layout-px-spacing">
    <div class="middle-content container-xxl p-0">
        <div id="tableCustomBasic" class="col-lg-12 col-12 layout-spacing">
            <div class="d-flex justify-content-between align-items-center flex-wrap mb-4 mt-5 p-3 rounded shadow-sm bg-white border">
                <h2 class="fw-bold mb-0 text-dark">Liste des types de véhicules</h2>
                <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#inputFormModal">
                    <i class="fa fa-plus"></i> Ajouter
                </button>
            </div>
            <div class="statbox widget box box-shadow">
                <div class="widget-content widget-content-area">
                    <table id="zero-config" class="table style-1 dt-table-hover non-hover">
                        <thead>
                            <tr>
                                <th>Type Véhicule</th>
                                <th class="text-center">Modifier</th>
                                <th class="text-center">Supprimer</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($TypeVehicules as $TypeVehicule)
                            <tr>
                                <td>
                                    {{ $TypeVehicule->NomTypeVehicule }}
                                </td>
                                @if($TypeVehicule->id > $TypeVehiculesCount)
                                <td class="text-center">
                                    <div class="action-btns">
                                        <a href="{{ route('TypeVehicule.edit', $TypeVehicule->id) }}" class="btn btn-outline-primary btn-xs">
                                            <i class="fa fa-edit"></i>
                                        </a>
                                    </div>
                                </td>
                                <td class="text-center">
                                    <form method="POST" action="{{ route('TypeVehicule.destroy', $TypeVehicule->id) }}">
                                        @csrf
                                        @method('DELETE')
                                        <button type="submit" class="btn btn-xs btn-outline-danger btn-default show_confirm" title='Delete'>
                                            <i class="fa fa-times-circle"></i>
                                        </button>
                                    </form>
                                </td>
                                @else
                                <td class="text-center"></td>
                                <td class="text-center"></td>
                                @endif
                            </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal -->
<div class="modal fade inputForm-modal" id="inputFormModal" tabindex="-1" data-bs-backdrop="static" role="dialog" aria-labelledby="inputFormModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered modal-lg" role="document">
        <div class="modal-content bg-light ">
            <div class="modal-header" id="inputFormModalLabel">
                <h5 class="modal-title">Ajouter un type de véhicule</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-hidden="true"></button>
            </div>
            <div class="modal-body">
                <form class="mt-0" action="{{ route('TypeVehicule.store') }}" method="POST" enctype="multipart/form-data">
                    @csrf
                    <div class="mb-3 row">
                        <label for="inputFonction" class="col-sm-3 col-form-label">Type du véhicule</label>
                        <div class="col-sm-9">
                            <input type="text" class="form-control" id="inputFonction" name="NomTypeVehicule" required>
                            @error('NomTypeVehicule')
                            <span class="text-danger error">Ce champ est requis et doit comporter minimum 2 caractères</span>
                            @enderror
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-light-danger mt-2 mb-2 btn-no-effect" data-bs-dismiss="modal">Retour</button>
                        <button type="submit" class="btn btn-primary mt-2 mb-2 btn-no-effect">Ajouter</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
@endsection
@section('js')
<script type="text/javascript">
    $(document).ready(function() {
        $('.show_confirm').click(function(event) {
            event.preventDefault();
            const form = $(this).closest("form");

            Swal.fire({
                title: '<span style="color:#d32f2f; font-size:24px; font-weight:bold;">⚠️ Suppression définitive</span>'
                , html: `
                    <div style="font-size:16px; line-height:1.6;">
                        <p><strong>Êtes-vous certain de vouloir supprimer ce type véhicule ?</strong></p>
                        <p style="color:#b71c1c;">Cette action <u>ne pourra pas être annulée</u> !</p>
                    </div>
                `
                , icon: 'error'
                , showCancelButton: true
                , confirmButtonText: '<i class="fas fa-check"></i> Oui, supprimer'
                , cancelButtonText: '<i class="fas fa-times"></i> Annuler'
                , confirmButtonColor: '#e53935'
                , cancelButtonColor: '#9e9e9e'
                , background: '#fff'
                , customClass: {
                    popup: 'shadow border border-danger rounded-3 px-4 py-3'
                    , confirmButton: 'btn btn-danger px-4 py-2'
                    , cancelButton: 'btn btn-outline-secondary px-4 py-2'
                }
                , backdrop: `rgba(0,0,0,0.4)`
            }).then((result) => {
                if (result.isConfirmed) {
                    form.submit();
                }
            });
        });
    });

</script>
@endsection
