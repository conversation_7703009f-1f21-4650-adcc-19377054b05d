<?php

namespace App\Http\Controllers;

use App\Models\CardPressoInfoCard;
use App\Models\Carte;
use Illuminate\Http\Request;
use MercurySeries\Flashy\Flashy;

class ImpressionController extends Controller
{
    public function index()
    {
        $cartes = Carte::where('StatutApprobation', 1)->get();
        return view('impression.index', compact('cartes'));
    }

    public function change_state_approved_printer(string $id)
    {
        $carte = Carte::find($id);
        $carte->StatutApprobation = 2;
        $carte->save();
        CardPressoInfoCard::where('NumeroAutorisationCarte', $carte->NumeroAutorisation)
            ->first()
            ->delete();
        Flashy::primary("Impression de la carte confirmé avec succès");
        return redirect()->back();
    }
}
