<?php

namespace App\Http\Controllers;

use App\Models\Beneficiaire;
use App\Models\FormeJuridique;
use App\Models\MarqueVoiture;
use App\Models\TypeVehicule;
use App\Models\Vehicule;
use Illuminate\Http\Request;
use MercurySeries\Flashy\Flashy;

class BeneficiaireController extends Controller
{
    public function index()
    {
        $user_id = auth()->user()->id;
        $beneficiaires = Beneficiaire::all();
        return view('beneficiaire.index', compact('beneficiaires'));
    }

    public function create()
    {
        $formes = FormeJuridique::all();
        return view('beneficiaire.create', compact('formes'));
    }

    public function store(Request $request)
    {
        $this->validate($request, [
            'Email' => 'required|email|unique:beneficiaires',
        ]);
        Beneficiaire::create([
            'RaisonSociale' => $request->RaisonSociale,
            'forme_juridique_id' => $request->formeJuridique_id,
            'Rccm' => $request->Rccm,
            'Adresse' => $request->Adresse,
            'Telephone' => $request->Telephone,
            'Email' => $request->Email,
            'NomGerant' => $request->NomGerant,
            'PrenomGerant' => $request->PrenomGerant,
            'Commentaire' => $request->Commentaire,
            'info_pdf' => isset($request->info_pdf) ? $request->info_pdf = upload($request->info_pdf, 'doc') : null,
            'user_id' => auth()->user()->id
        ]);
        Flashy::primary("Enregistrement bénéficiaire éffectué avec succès");
        return to_route('beneficiaire.index');
    }

    public function show(Beneficiaire $beneficiaire)
    {
        $beneficiaire = Beneficiaire::find($beneficiaire->id);
        $type_vehicules = TypeVehicule::all();
        $marqueVehicules = MarqueVoiture::all();
        $vehicules = Vehicule::where('beneficiaire_id', $beneficiaire->id)->get();
        return view('vehicules.index', compact('beneficiaire', 'type_vehicules', 'marqueVehicules', 'vehicules'));
    }

    public function edit(Beneficiaire $beneficiaire)
    {
        $beneficiaire = Beneficiaire::where('id', $beneficiaire->id)->first();
        $formes = FormeJuridique::all();
        return view('beneficiaire.edit', compact('beneficiaire', 'formes'));
    }

    public function update(Request $request, Beneficiaire $beneficiaire)
    {
        $beneficiaire = Beneficiaire::where('id', $beneficiaire->id)->first();
        $beneficiaire->RaisonSociale = $request->RaisonSociale;
        $beneficiaire->forme_juridique_id = $request->formeJuridique_id;
        $beneficiaire->Rccm = $request->Rccm;
        $beneficiaire->Adresse = $request->Adresse;
        $beneficiaire->Telephone = $request->Telephone;
        $beneficiaire->Email = $request->Email;
        $beneficiaire->NomGerant = $request->NomGerant;
        $beneficiaire->PrenomGerant = $request->PrenomGerant;
        $beneficiaire->info_pdf = isset($request->info_pdf) ? $request->info_pdf = upload($request->info_pdf, 'doc') : null;
        $beneficiaire->update();
        Flashy::success("Modification bénéficiaire éffectuée avec succès");
        return redirect()->route('beneficiaire.index');
    }

    public function destroy(string $id)
    {
        $beneficiaire = Beneficiaire::find($id);
        if (!$beneficiaire) {
            Flashy::error('Bénéficiaire introuvable');
            return redirect()->back();
        }
        // Vérifie s'il y a des véhicules associés
        if ($beneficiaire->vehicules->count()) {
            Flashy::error("Impossible !! Bénéficiaire ayant déjà des véhicules");
        } else {
            $beneficiaire->delete();
            Flashy::warning('Béneficiaire supprimé avec succès');
        }
        return redirect()->route('beneficiaire.index');
    }
}
