@extends('layouts.base')
@section('content')
<div class="layout-px-spacing">
    <div class="middle-content container-xxl p-0">
        <div id="tableCustomBasic" class="col-lg-12 col-12 layout-spacing">
            <div class="d-flex justify-content-between align-items-center flex-wrap mb-4 mt-5 p-3 rounded shadow-sm bg-white border">
                <h2 class="fw-bold mb-0 text-dark">{{ $beneficiaire->RaisonSociale }}</h2>
                <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#modalLg">
                    <i class="fa fa-plus"></i> Nouveau véhicule
                </button>
            </div>
            <div>
                <a href="{{ route('beneficiaire.index')}}"><i class="fa fa-arrow-circle-left"></i> Bénéficiaires </a>
            </div>
            <div class="statbox widget box box-shadow">
                <div class="widget-content widget-content-area">
                    <table id="zero-config" class="table style-1 dt-table-hover non-hover">
                        <thead>
                            <tr>
                                <th>Immat</th>
                                <th>Marque</th>
                                <th>Modèle</th>
                                <th>Type</th>
                                <th>Couleur</th>
                                <th>Statut</th>
                                <th>Action</th>
                                <th>Modifier</th>
                                <th>Supprimer</th>
                                <th>PDF</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($vehicules as $vehicule)
                            <tr>
                                <td>{{ $vehicule->Immatriculation}}</td>
                                <td>{{ $vehicule->marqueVoiture->NomMarque}}</td>
                                <td>{{ $vehicule->Model}}</td>
                                <td>{{ $vehicule->type_vehicule->NomTypeVehicule}}</td>
                                <td>{{ $vehicule->Couleur}}</td>
                                <td>
                                    @if($vehicule->Statut == 0)
                                    <span class="badge badge-light-danger rounded fs-12px d-inline-flex align-items-center"><i class="fa fa-circle fs-9px fa-fw me-5px"></i>Désactivé</span>
                                    @else
                                    <span class="badge badge-light-success bg-opacity-15 px-2 py-6px rounded fs-12px d-inline-flex align-items-center"><i class="fa fa-circle fs-9px fa-fw me-5px"></i> Activé</span>
                                    @endif
                                </td>
                                <td>
                                    <form action="{{ route('change_vehicule_state', $vehicule->id) }}" method="GET" id="submit{{ $vehicule->id}}">
                                        @if($vehicule->Statut === 0)
                                        <button type="button" onclick="changeAction('{{ $vehicule->id}}')" class="btn btn-outline-success btn-sm">Activer</button>
                                        @else
                                        <button type="button" onclick="changeAction('{{ $vehicule->id}}')" class="btn btn-outline-danger btn-sm">Désactiver</button>
                                        @endif
                                    </form>
                                </td>
                                <td>
                                    <!-- Form Button trigger modal -->
                                    <a href="{{ route('vehicule.edition', [$vehicule->id, $beneficiaire->id]) }}" class="btn btn-outline-primary btn-xs">
                                        <i class="fa fa-edit"></i>
                                    </a>
                                </td>
                                <td>
                                    <form method="POST" action="{{ route('vehicule.destroy', $vehicule->id) }}">
                                        @csrf
                                        @method('DELETE')
                                        <input type="hidden" name="beneficiaire_id" value="{{ $beneficiaire->id ?? '' }}">
                                        <button type="button" class="btn btn-xs btn-outline-danger btn-default show_confirm" title='Suppression du véhicule'>
                                            <i class="fa fa-times-circle"></i>
                                        </button>
                                    </form>
                                </td>
                                <td>
                                    @if($vehicule->info_pdf != null)
                                    <a href="{{ $vehicule->pdf_url }}" target="_blank" class="table-inner-text">Voir pdf</a>
                                    @else
                                    -
                                    @endif
                                </td>
                            </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- ajouter un vehicule -->
<div class="modal fade" id="modalLg" data-bs-backdrop="static">
    <div class="modal-dialog modal-lg">
        <div class="modal-content bg-light">
            <div class="modal-header">
                <div class="modal-title fs-5">Ajouter un nouveau véhicule</div>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="FormValidationVehicule" action="{{ route('vehicule.store')}}" method="POST" enctype="multipart/form-data">
                    @csrf
                    <input type="hidden" name="beneficiaire_id" value="{{ $beneficiaire->id ?? '' }}">
                    <div class="mb-3 row">
                        <label for="Immatriculation" class="col-sm-4 col-form-label">Numero d'immatriculation *</label>
                        <div class="col-sm-8">
                            <input type="text" class="form-control" id="Immatriculation" name="Immatriculation">
                        </div>
                    </div>
                    <div class="mb-3 row">
                        <label for="marque_voiture_id" class="col-sm-4 col-form-label">Marque de la voiture *</label>
                        <div class="col-sm-8">
                            <select class="form-select" id="marque_voiture_id" name="marque_voiture_id">
                                @foreach($marqueVehicules as $marqueVehicule)
                                <option value="{{ $marqueVehicule->id}}">{{ $marqueVehicule->NomMarque}}</option>
                                @endforeach
                            </select>
                        </div>
                    </div>

                    <div class="mb-3 row">
                        <label for="Model" class="col-sm-4 col-form-label">Modèle de la voiture *</label>
                        <div class="col-sm-8">
                            <input type="text" class="form-control" id="Model" name="Model" required>
                        </div>
                    </div>
                    <div class="mb-3 row">
                        <label for="type_vehicule_id" class="col-sm-4 col-form-label">Type de la voiture *</label>
                        <div class="col-sm-8">
                            <select name="type_vehicule_id" id="type_vehicule_id" class="form-control">
                                @foreach($type_vehicules as $type_vehicule)
                                <option value="{{ $type_vehicule->id }}">{{ $type_vehicule->NomTypeVehicule}}</option>
                                @endforeach
                            </select>
                        </div>
                    </div>
                    <div class="mb-3 row">
                        <label for="Couleur" class="col-sm-4 col-form-label">Couleur de la voiture *</label>
                        <div class="col-sm-8">
                            <input type="text" class="form-control" id="Couleur" name="Couleur">
                        </div>
                    </div>
                    <div class="mb-3 row">
                        <label for="info_pdf" class="col-sm-2 col-form-label">Infos du vehicule (PDF)</label>
                        <div class="col-sm-10">
                            <input type="file" name="info_pdf" id="info_pdf" class="form-control">
                        </div>
                    </div>

                    <div class="mb-3 row">
                        <label for="Description" class="col-sm-4 col-form-label">Description</label>
                        <div class="col-sm-8">
                            <textarea type="text" name="Description" id="Description" class="form-control" cols="5" rows="4" value="{{ old('Description')}}"> </textarea>
                        </div>
                    </div>

                    <div class="form-group row">
                        <div class="col-sm-8 mt-3">
                            <button type="submit" class="btn btn-dark ">Enregistrer la voiture</button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
@endsection
@section('js')
<script src="{{asset('static/src/assets/js/jsPage/form-create-vehicule.js') }}"></script>
<script>
    $(document).on('click', '.show_confirm', function(e) {
        e.preventDefault();
        const form = $(this).closest('form');

        Swal.fire({
            title: '<span style="color:#d32f2f; font-size:24px; font-weight:bold;">⚠️ Suppression définitive</span>'
            , html: `
        <div style="font-size:16px; line-height:1.6;">
            <p><strong>Êtes-vous certain de vouloir supprimer ce véhicule ?</strong></p>
            <p style="color:#b71c1c;">Cette action <u>ne pourra pas être annulée</u> !</p>
        </div>
    `
            , icon: 'error'
            , showCancelButton: true
            , confirmButtonText: '<i class="fas fa-check"></i> Oui, supprimer'
            , cancelButtonText: '<i class="fas fa-times"></i> Annuler'
            , confirmButtonColor: '#e53935'
            , cancelButtonColor: '#9e9e9e'
            , background: '#fff'
            , customClass: {
                popup: 'shadow border border-danger rounded-3 px-4 py-3'
                , confirmButton: 'btn btn-danger px-4 py-2'
                , cancelButton: 'btn btn-outline-secondary px-4 py-2'
            }
            , backdrop: `
        rgba(0,0,0,0.4)
        left top
        no-repeat
    `
        }).then((result) => {
            if (result.isConfirmed) {
                form.submit();
            }
        });
    });

    function changeAction(id) {
        let form = document.querySelector(`#submit${id}`);
        Swal.fire({
            title: 'Changement de statut'
            , html: '<strong style="color:#e2a03f">Vous êtes sur le point de change le statut du véhicule.</strong>'
            , icon: 'question'
            , showCancelButton: true
            , confirmButtonText: 'Oui, changer'
            , cancelButtonText: 'Annuler'
            , confirmButtonColor: '#f79102'
            , cancelButtonColor: '#87adbd'
            , customClass: {
                popup: 'shadow-lg rounded-lg'
                , confirmButton: 'btn btn-warning'
                , cancelButton: 'btn btn-secondary me-2'
            }
        }).then((result) => {
            if (result.isConfirmed) {
                form.submit();
            }
        });
    }

</script>
@endsection
