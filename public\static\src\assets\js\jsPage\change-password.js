$.validator.addMethod("strongPassword", function (value, element, param) {
    if (!/[A-Z]/.test(value)) {
        $.validator.messages.strongPassword = "Au moins une lettre majuscule.";
        return false;
    }
    if (!/[0-9]/.test(value)) {
        $.validator.messages.strongPassword = "Au moins un chiffre.";
        return false;
    }
    if (!/[\W_]/.test(value)) {
        $.validator.messages.strongPassword = "Au moins un caractère spécial.";
        return false;
    }
    if (value.length < 8) {
        $.validator.messages.strongPassword = "Au moins 8 caractères.";
        return false;
    }

    return true;
});

$(document).ready(function () {
    $('#FormValidationPassword').validate({
        rules: {
            password: {
                required: true
                , strongPassword: true
            }
            , password_confirmation: {
                required: true
                , equalTo: "#password"
            }
        }
        , messages: {
            password: {
                required: "Mot de passe requis"
            }
            , password_confirmation: {
                required: "Confirmez le mot de passe"
                , equalTo: "Les mots de passe ne correspondent pas"
            }
        }
        , errorElement: 'span'
        , errorClass: 'text-danger'
        , highlight: function (element) {
            $(element).addClass('is-invalid');
        }
        , unhighlight: function (element) {
            $(element).removeClass('is-invalid');
        }
    });

});
