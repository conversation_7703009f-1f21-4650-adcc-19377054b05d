<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('card_presso_info_cards', function (Blueprint $table) {
            $table->id();
            $table->string('NumeroAutorisationCarte');
            $table->string('NomBeneficiaire');
            $table->string('NumeroImmatriculationVehicule');
            $table->string('DateApprobation')->nullable();
            $table->string('DateExpiration')->nullable();
            $table->text('ZoneAccess')->nullable();
            $table->text('QrCode')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('card_presso_info_cards');
    }
};
