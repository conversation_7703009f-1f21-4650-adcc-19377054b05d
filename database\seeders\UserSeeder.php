<?php

namespace Database\Seeders;

use App\Models\User;
use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Role;

class UserSeeder extends Seeder
{
    public function run(): void
    {
        $role = Role::where('name', 'Administrateur')->first();
        $user = User::create([
            'Code' => 'ITS00001',
            'Nom' => 'Administrateur',
            'Prenom' => 'ITS',
            'Sexe' => '',
            'Fonction' => 'Administrateur',
            'Telephone' => '662686868',
            'Email' => '<EMAIL>',
            'password' => bcrypt('Admin@@@223'),
            'Photo' => '',
            'Statut' => 1,
            'profile_id' => $role->id,
            'parent_id' => 1,
        ]);
        $user->assignRole([$role->id]);
    }
}
