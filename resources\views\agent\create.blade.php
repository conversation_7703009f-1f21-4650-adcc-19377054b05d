@extends('layouts.base')
@section('content')
<x-breadcrumb :current="'Nouveau agent'" previous="Agent" :previousRoute="route('agent.index')" />
<div class="layout-px-spacing">
    <div class="middle-content container-xxl p-0">
        <div class="row">
            <div id="flStackForm" class="col-lg-12 layout-spacing layout-top-spacing">
                <div class="statbox widget box box-shadow">
                    <div class="widget-header">
                        <div class="row">
                            <div class="col-xl-12 col-md-12 col-sm-12 col-12">
                                <h4>Ajouter un nouveau agent</h4>
                            </div>
                        </div>
                    </div>
                    <div class="widget-content widget-content-area">
                        <form id="FormValidationAgent" action="{{ route('agent.store')}}" method="POST" enctype="multipart/form-data">
                            @csrf
                            <div class="mb-3 row">
                                <label for="CodeAgent" class="col-sm-2 col-form-label">Code Agent *</label>
                                <div class="col-sm-10">
                                    <input type="text" class="form-control text-black" id="CodeAgent" name="CodeAgent" value="{{ $code_agent }}" readonly>
                                    @error('CodeAgent') <span class="text-danger error">{{ $message }}</span>@enderror
                                </div>
                            </div>
                            <div class="mb-3 row">
                                <label for="NomAgent" class="col-sm-2 col-form-label">Nom Agent *</label>
                                <div class="col-sm-10">
                                    <input type="text" class="form-control" id="NomAgent" name="NomAgent" value="{{ old('NomAgent') }}">
                                </div>
                            </div>

                            <div class="mb-3 row">
                                <label for="PrenomAgent" class="col-sm-2 col-form-label">Prenom Agent *</label>
                                <div class="col-sm-10">
                                    <input type="text" class="form-control" id="PrenomAgent" name="PrenomAgent" value="{{ old('PrenomAgent') }}">
                                </div>
                            </div>

                            <div class="mb-3 row">
                                <label for="Sexe" class="col-sm-2 col-form-label">Sexe *</label>
                                <div class="col-sm-10">
                                    <select name="Sexe" id="Sexe" class="form-control">
                                        <option value="M">Masculin</option>
                                        <option value="F">Féminin</option>
                                    </select>
                                </div>
                            </div>

                            <div class="mb-3 row">
                                <label for="Telephone" class="col-sm-2 col-form-label">Telephone *</label>
                                <div class="col-sm-10">
                                    <input type="text" class="form-control" id="Telephone" name="Telephone" value="{{ old('Telephone') }}" onblur="validerNumero()">
                                </div>
                            </div>

                            <div class="mb-3 row">
                                <label for="Photo" class="col-sm-2 col-form-label">Photo *</label>
                                <div class="col-sm-10">
                                    <input type="file" class="form-control" id="Photo" name="Photo" accept=".jpg,.jpeg,.png">
                                </div>
                            </div>

                            <div class="mb-3 row">
                                <label for="Pseudo" class="col-sm-2 col-form-label">Pseudo * (<span>Identifiant pour vous connecter</span>) </label>
                                <div class="col-sm-10">
                                    <input type="text" class="form-control" id="Pseudo" name="Pseudo" value="{{ old('Pseudo') }}">
                                    @error('Pseudo') <span class="text-danger error">{{ $message }}</span>@enderror
                                </div>
                            </div>

                            <div class="mb-3 row">
                                <label for="profile" class="col-sm-2 col-form-label">Profile *</label>
                                <div class="col-sm-10">
                                    <select name="profile" id="profile" class="form-control">
                                        <option value="" selected disabled>Selectionner le role</option>
                                        @foreach($profiles as $profile)
                                        @if($profile->StatutProfile == 'mobile' )
                                        <option value="{{ $profile->id}}">{{ $profile->name }}</option>
                                        @endif
                                        @endforeach
                                    </select>
                                    @error('profile') <span class="text-danger error">{{ $message }}</span>@enderror
                                </div>
                            </div>
                            <div class="mb-4 row">
                                <label for="password" class="col-sm-2 col-form-label">
                                    Mot de passe *
                                </label>

                                <div class="col-sm-10">
                                    <div class="password-wrapper" style="position: relative;">
                                        <input type="password" class="form-control" id="password" name="password" style="padding-right: 40px;" oncopy="return false;" onpaste="return false;" oncut="return false;">
                                        <i id="togglePassword" class="fa-solid fa-eye icon-eye" style="position: absolute; top: 50%; right: 10px; transform: translateY(-50%); cursor: pointer;"></i>
                                    </div>

                                    <ul id="password-rules" class="mt-2">
                                        <li id="rule-length" class="invalid">
                                            <i class="fa-solid fa-circle-xmark icon"></i> Au moins 8 caractères
                                        </li>
                                        <li id="rule-uppercase" class="invalid">
                                            <i class="fa-solid fa-circle-xmark icon"></i> Au moins une lettre majuscule
                                        </li>
                                        <li id="rule-number" class="invalid">
                                            <i class="fa-solid fa-circle-xmark icon"></i> Au moins un chiffre
                                        </li>
                                        <li id="rule-special" class="invalid">
                                            <i class="fa-solid fa-circle-xmark icon"></i> Au moins un caractère spécial (!@#...)
                                        </li>
                                    </ul>
                                </div>
                            </div>

                            <div class="mb-3 row">
                                <label for="password_confirmation" class="col-sm-2 col-form-label">Confirmation *</label>
                                <div class="col-sm-10">
                                    <input type="password" class="form-control" oncopy="return false;" onpaste="return false;" oncut="return false;" placeholder="confirmer votre mot de passe" name="password_confirmation" id="password_confirmation" aria-describedby="passwordHelpBlock" autocomplete="off">
                                </div>
                            </div>

                            <div class="form-group row">
                                <div class="col-sm-10 offset-sm-2 mt-3">
                                    <a href="{{ URL::previous() }}" class="btn btn-danger">Annuler</a>
                                    <button type="submit" class="btn btn-dark">Ajouter l'agent</button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>

        </div>
    </div>
</div>
@endsection
@push('scripts')
<script src="{{asset('static/src/assets/js/jsPage/form-create-agent.js') }}"></script>
@endpush
