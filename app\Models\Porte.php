<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Porte extends Model
{
    use HasFactory;

    protected $fillable = [
        'CodePorte',
        'Description',
        'zone_id',
        'qrCode'
    ];

    public function zone(){
        return $this->belongsTo(Zone::class);
    }

    public function scans()
    {
        return $this->hasMany(Scan::class);
    }
}
