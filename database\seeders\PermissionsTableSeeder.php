<?php

namespace Database\Seeders;
use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;
use Carbon\Carbon;

class PermissionsTableSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        //
        $data = [
            // pour le mobile 

            [
                'name' => 'scan.view',
                'label' => 'Scanner par le mobile'

            ],

            // Utilisateurs web
            [
                'name' => 'utilisateur.view',
                'label'=> 'voir le menu compte web'
            ],
            [
                'name' => 'utilisateur.create',
                'label'=> 'creer un utilisateur'
            ],
            [
                'name' => 'utilisateur.edit',
                'label'=> 'Modifier un utilisateur'
            ],
            [
                'name' => 'utilisateur.delete',
                'label'=> 'Supprimer un utilisateur'
            ],
            // agents mobile
            [
                'name' => 'agent.view',
                'label'=> 'voir le menu compte mobile'
            ],
            [
                'name' => 'agent.create',
                'label'=> 'creer un agent'
            ],
            [
                'name' => 'agent.edit',
                'label'=> 'Modifier un agent'
            ],
            [
                'name' => 'agent.delete',
                'label'=> 'Supprimer un agent'
            ],
            // Acces aux differents roles
            [
                'name' => 'profile.view',
                'label'=> 'voir les profiles'
            ],
            [
                'name' => 'profile.create',
                'label'=> 'creer un profile'
            ],
            [
                'name' => 'profile.edit',
                'label'=> 'Modifier un profile'
            ],
            [
                'name' => 'profile.delete',
                'label'=> 'Supprimer un profile'
            ],
            // Beneficiaire
            [
                'name' => 'beneficiaire.view',
                'label'=> 'voir le menu beneficiaire'
            ],
            [
                'name' => 'beneficiaire.create',
                'label'=> 'creer un beneficiaire'
            ],
            [
                'name' => 'beneficiaire.show',
                'label'=> 'voir les details du beneficiaire'
            ],
            [
                'name' => 'beneficiaire.edit',
                'label'=> 'Modifier un beneficiaire'
            ],
            [
                'name' => 'beneficiaire.delete',
                'label'=> 'supprimer un beneficiaire'
            ],
            // vehicule
            [
                'name' => 'vehicule.create',
                'label'=> 'creer un vehicule'
            ],
            [
                'name' => 'vehicule.edit',
                'label'=> 'modifier un vehicule'
            ],
            [
                'name' => 'vehicule.delete',
                'label'=> 'supprimer un vehicule'
            ],
            // type vehicule
            [
                'name' => 'TypeVehicule.view',
                'label'=> 'voir le menu type vehicule'
            ],
            [
                'name' => 'TypeVehicule.create',
                'label'=> 'creer un type vehicule'
            ],
            [
                'name' => 'TypeVehicule.edit',
                'label'=> 'modifier un type vehicule'
            ],
            [
                'name' => 'TypeVehicule.delete',
                'label'=> 'supprimer un type vehicule'
            ],
            // FormeJuridique
            [
                'name' => 'FormeJuridique.view',
                'label'=> 'voir le menu forme juridique'
            ],
            [
                'name' => 'FormeJuridique.create',
                'label'=> 'creer une forme juridique'
            ],
            [
                'name' => 'FormeJuridique.edit',
                'label'=> 'modifier une forme juridique'
            ],
            [
                'name' => 'FormeJuridique.delete',
                'label'=> 'supprimer une forme juridique'
            ],

             // Marque des Terminaux
             [
                'name' => 'marque.view',
                'label'=> 'voir le menu marque'
            ],
            [
                'name' => 'marque.create',
                'label'=> 'creer une marque'
            ],
            [
                'name' => 'marque.edit',
                'label'=> 'modifier une marque'
            ],
            [
                'name' => 'marque.delete',
                'label'=> 'supprimer une marque'
            ],

            // Marque des voitures
            [
                'name' => 'marqueVoiture.view',
                'label'=> 'voir le menu marque voiture'
            ],
            [
                'name' => 'marqueVoiture.create',
                'label'=> 'creer une marque de voiture'
            ],
            [
                'name' => 'marqueVoiture.edit',
                'label'=> 'modifier une marque de voiture'
            ],
            [
                'name' => 'marqueVoiture.delete',
                'label'=> 'supprimer une marque de voiture'
            ],

            // StatutTerminal
            [
                'name' => 'StatutTerminal.view',
                'label'=> 'voir le menu Statut Terminal'
            ],
            [
                'name' => 'StatutTerminal.create',
                'label'=> 'creer une zone'
            ],
            [
                'name' => 'StatutTerminal.edit',
                'label'=> 'modifier une zone'
            ],
            [
                'name' => 'StatutTerminal.delete',
                'label'=> 'supprimer une zone'
            ],

            // zone
            [
                'name' => 'zone.view',
                'label'=> 'voir le menu zone'
            ],
            [
                'name' => 'zone.create',
                'label'=> 'creer une zone'
            ],
            [
                'name' => 'zone.edit',
                'label'=> 'modifier une zone'
            ],
            [
                'name' => 'zone.delete',
                'label'=> 'supprimer une zone'
            ],
            // portes
            [
                'name' => 'porte.create',
                'label'=> 'creer une porte'
            ],
            [
                'name' => 'porte.edit',
                'label'=> 'modifier une porte'
            ],
            [
                'name' => 'porte.delete',
                'label'=> 'supprimer une porte'
            ],
            // Terminal
            [
                'name' => 'terminal.view',
                'label'=> 'voir le menu terminal'
            ],
            [
                'name' => 'terminal.create',
                'label'=> 'voir le menu terminal'
            ],
            [
                'name' => 'terminal.edit',
                'label'=> 'modifier un terminal'
            ],
            [
                'name' => 'terminal.delete',
                'label'=> 'supprimer un terminal'
            ],
            // 
             // assignation
             [
                'name' => 'assignation.view',
                'label'=> 'voir le menu Assignation'
            ],
            [
                'name' => 'assignation.create',
                'label'=> 'creer une nouvelle assignation'
            ],
            [
                'name' => 'assignation.edit',
                'label'=> 'Modifier une assignation'
            ],
            [
                'name' => 'assignation.delete',
                'label'=> 'Supprimer une assignation'
            ],

            // access
             // assignation
             [
                'name' => 'carte.view',
                'label'=> 'voir le menu gestion acces'
            ],
            [
                'name' => 'carte.create',
                'label'=> 'Generer une nouvelle carte'
            ],
            [
                'name' => 'carte.edit',
                'label'=> 'Modifier une carte'
            ],
            [
                'name' => 'carte.delete',
                'label'=> 'Supprimer une carte'
            ],

            //Impression carte
            [
                'name' => 'impression.view',
                'label'=> 'Voir la liste des cartes imprimables'
            ],

            //Approbation carte
            [
                'name' => 'approbation.view',
                'label'=> 'Voir la liste des cartes en attente d\'approbation'
            ],

            //Reactivation carte
            [
                'name' => 'reactivation.view',
                'label'=> 'Voir la liste des cartes à reactiver'
            ],

            //Desactivation carte
            [
                'name' => 'desactivation.view',
                'label'=> 'Voir la liste des cartes à désactiver'
            ],
            //Desactivation carte
            [
                'name' => 'rapports.view',
                'label'=> 'Voir les statistiques de beneficiaire'
            ],
        
        ];
        
        $insert_data = [];
        $time_stamp = Carbon::now()->toDateTimeString();
        foreach ($data as $d) {
            $d['guard_name'] = 'web';
            $d['created_at'] = $time_stamp;
            $insert_data[] = $d;

        }
        Permission::insert($insert_data);
        $role = Role::create(['name' => 'Administrateur', 'StatutProfile' => 'web']);
        foreach (Permission::all() as $permission) {
            $role->givePermissionTo($permission);
        }
    }
}
