@extends('layouts.base')
@section('content')
<div class="layout-px-spacing">
    <div class="middle-content container-xxl p-0">
        <div id="tableCustomBasic" class="col-lg-12 col-12 layout-spacing">
            <div class="d-flex justify-content-between align-items-center flex-wrap mb-4 mt-5 p-3 rounded shadow-sm bg-white border">
                <h2 class="fw-bold mb-0 text-dark">Liste des zones</h2>
                <a href="{{ route('zone.create') }}" class="btn btn-primary d-flex align-items-center">
                    <i class="fa fa-plus me-2"></i> Nouveau
                </a>
            </div>
            <div class="statbox widget box box-shadow">
                <div class="widget-content widget-content-area">
                    <table id="zero-config" class="table style-1 dt-table-hover non-hover">
                        <thead>
                            <tr>
                                <th class="">Code de la zone</th>
                                <th class="">Nom de la zone</th>
                                <th class="">Nombre de portes</th>
                                <th>Action</th>
                                <th>Suppression</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($zones as $zone)
                            <tr>
                                <td class="">{{ $zone->CodeZone}} </td>
                                <td class="">{{ $zone->NomZone}} </td>
                                <td> {{ $zone->porte->count()}} </td>
                                <td class="">
                                    <div class="action-btns">
                                        <a href="{{ route('zone.show', $zone->id) }}" class="action-btn btn-view bs-tooltip me-2" data-toggle="tooltip" data-placement="top" title="View">
                                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-eye">
                                                <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"></path>
                                                <circle cx="12" cy="12" r="3"></circle>
                                            </svg>
                                        </a>
                                        <a href="{{ route('zone.edit', $zone->id) }}" class="mt-2 edit-profile"> <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-edit-3">
                                                <path d="M12 20h9"></path>
                                                <path d="M16.5 3.5a2.121 2.121 0 0 1 3 3L7 19l-4 1 1-4L16.5 3.5z"></path>
                                            </svg>
                                        </a>
                                    </div>
                                </td>
                                <td>
                                    @if (!$zone->porte()->exists())
                                    <div class="action-btns">
                                        <form method="POST" action="{{ route('zone.destroy', $zone->id) }}">
                                            @csrf
                                            @method('DELETE')
                                            <button type="submit" class="btn btn-xs btn-outline-danger btn-default show_confirm" title='Delete'><i class="fa fa-times-circle"></i></button>
                                        </form>
                                    </div>
                                    @else
                                    -
                                    @endif
                                </td>
                            </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
@section('js')
<script type="text/javascript">
    $('.show_confirm').click(function(event) {
        var form = $(this).closest("form");
        var name = $(this).data("name");
        event.preventDefault();
        Swal.fire({
            title: '<span style="color:#d32f2f; font-size:24px; font-weight:bold;">⚠️ Suppression définitive</span>'
            , html: `
        <div style="font-size:16px; line-height:1.6;">
            <p><strong>Êtes-vous certain de vouloir supprimer cette zone ?</strong></p>
            <p style="color:#b71c1c;">Cette action <u>ne pourra pas être annulée</u> !</p>
        </div>
    `
            , icon: 'error'
            , showCancelButton: true
            , confirmButtonText: '<i class="fas fa-check"></i> Oui, supprimer'
            , cancelButtonText: '<i class="fas fa-times"></i> Annuler'
            , confirmButtonColor: '#e53935'
            , cancelButtonColor: '#9e9e9e'
            , background: '#fff'
            , customClass: {
                popup: 'shadow border border-danger rounded-3 px-4 py-3'
                , confirmButton: 'btn btn-danger px-4 py-2'
                , cancelButton: 'btn btn-outline-secondary px-4 py-2'
            }
            , backdrop: `
        rgba(0,0,0,0.4)
        left top
        no-repeat
    `
        }).then((result) => {
            if (result.isConfirmed) {
                form.submit();
            }
        });
    });

</script>
@endsection
