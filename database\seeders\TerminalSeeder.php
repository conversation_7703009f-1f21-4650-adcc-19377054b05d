<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class TerminalSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        DB::table('terminals')->insert([
            [
                'NomTerminal' => "Samsung Galaxy A14",
                'AdresseMac' => "00:1A:2B:3C:4D:5E",
                'NumeroSerie' => "SN12345A67890XYZ",
                'Model' => "Galaxy A14",
                'marque_id' => 1,
                'statut_terminal_id' => 1,
                'user_id' => 1
            ],
            [
                'NomTerminal' => "Samsung Galaxy X",
                'AdresseMac' => "D4:F5:6B:7C:8D:9A",
                'NumeroSerie' => "A1B2C3D4E5F6G7H8",
                'Model' => "Galaxy X",
                'marque_id' => 1,
                'statut_terminal_id' => 1,
                'user_id' => 1
            ],
            [
                'NomTerminal' => "Techno POP 5",
                'AdresseMac' => "3E:2F:1D:9A:B0:C1",
                'NumeroSerie' => "HTC-9876543210XYZ",
                'Model' => "POP 5",
                'marque_id' => 2,
                'statut_terminal_id' => 1,
                'user_id' => 1
            ],
            [
                'NomTerminal' => "Huawei Mate X6",
                'AdresseMac' => "C0:A8:0B:1C:2D:3F",
                'NumeroSerie' => "SNX-3210PQR5678TUV",
                'Model' => "Mate X6",
                'marque_id' => 3,
                'statut_terminal_id' => 1,
                'user_id' => 1
            ]
        ]);
    }
}
