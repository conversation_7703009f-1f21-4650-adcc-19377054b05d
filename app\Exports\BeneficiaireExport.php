<?php

namespace App\Exports;

use App\Models\Beneficiaire;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\WithStyles;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;

class BeneficiaireExport implements FromCollection, WithHeadings, WithMapping, WithStyles
{
    /**
     * @return \Illuminate\Support\Collection
     */
    public function collection()
    {
        return Beneficiaire::all();
    }

    // Définir les entêtes de colonnes
    public function headings(): array
    {
        return [
            'id',
            'RaisonSociale',
            'Rccm',
            'NomFormeJuridique',
            'Telephone',
            'Adresse',
            'Email',
            'Identité gérant',
            'Commentaire',
            'Identité utilisateur'
        ];
    }

    // Mapper les colonnes de chaque ligne
    public function map($beneficiaire): array
    {
        return [
            (string)($beneficiaire->id),
            $beneficiaire->RaisonSociale,
            $beneficiaire->Rccm,
            $beneficiaire->forme_juridique->NomFormeJuridique,
            $beneficiaire->Telephone,
            $beneficiaire->Adresse,
            $beneficiaire->Email,
            $beneficiaire->PrenomGerant . ' ' . $beneficiaire->NomGerant,
            $beneficiaire->Commentaire,
            $beneficiaire->user->Prenom . ' ' . $beneficiaire->user->Nom,
        ];
    }

    // Style pour la ligne des entêtes
    public function styles(Worksheet $sheet)
    {
        return [
            1 => [
                'font' => [
                    'bold' => true,
                    'size' => 13, // ✅ Taille de police augmentée
                ]
            ],
        ];
    }
}
