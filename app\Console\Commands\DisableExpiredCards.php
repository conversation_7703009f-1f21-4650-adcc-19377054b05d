<?php

namespace App\Console\Commands;

use App\Models\Carte;
use Carbon\Carbon;
use Illuminate\Console\Command;

class DisableExpiredCards extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'cards:disable-expired'; //Signature de la commande

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = "Désactivation de toutes les cartes dont la date d'expiration est atteinte";

    public function __construct()
    {
        parent::__construct();
    }


    /**
     * Execute the console command.
     */
    public function handle()
    {
        $today = Carbon::now(); // Obtenir la date du jour

        // Désactiver les cartes dont la date d'expiration est passée
        $cartes = Carte::whereDate('DateExpiration', $today) //Comparer la date d'expiration à la date dont la commande s'exécute
            ->where('Statut', 1) // Si nécessaire, pour ne désactiver que les cartes actives
            ->update(['Statut' => 0]);

        $this->info("Toutes les cartes expirées ont été désactiver avec succès");
    }
}
