<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\PDFController;
use App\Http\Controllers\UserController;
use App\Http\Controllers\ZoneController;
use App\Http\Controllers\AgentController;
use App\Http\Controllers\CarteController;
use App\Http\Controllers\ExcelController;
use App\Http\Controllers\LoginController;
use App\Http\Controllers\PorteController;
use App\Http\Controllers\MarqueController;
use App\Http\Controllers\ProfileController;
use App\Http\Controllers\RapportController;
use App\Http\Controllers\TerminalController;
use App\Http\Controllers\VehiculeController;
use App\Http\Controllers\AgentAuthController;
use App\Http\Controllers\DashboardController;
use App\Http\Controllers\ParametreController;
use App\Http\Controllers\ImpressionController;
use App\Http\Controllers\AssignationController;
use App\Http\Controllers\BeneficiaireController;
use App\Http\Controllers\TypeVehiculeController;
use App\Http\Controllers\MarqueVoitureController;
use App\Http\Controllers\DesassignationController;
use App\Http\Controllers\FormeJuridiqueController;
use App\Http\Controllers\StatutTerminalController;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "web" middleware group. Make something great!
|
*/

//GESTION AUTHENTIFICATION
Route::get('', [LoginController::class, 'loginView'])->name('login');
Route::post('/authentification-utilisateur', [LoginController::class, 'defLogUser'])->name('def_log_user');

//GROUPEMENT DE TOUTES LES ROUTES QUI PASSE PAR L'AUTH D'ABORD
Route::middleware(['auth', 'CheckStatus'])->group(function () {
    //TABLEAU DE BORD
    Route::get('/tableau-de-board', [DashboardController::class, 'dashboard'])->name('dashboard');

    //GESTION DES IMPORTS ET EXPORT
    Route::get('/export-beneficiaire', [ExcelController::class, 'beneficiaireExport'])->name('beneficiaire.export');
    // Route::post('/import-beneficiaire', [ExcelController::class, 'beneficiaireImport'])->name('beneficiaire.import');

    //GENERER UN QR CODE POUR LA PORTE
    Route::get('/downloadQrCodePorte/{porteId}', [ZoneController::class, 'downloadQRCodePorte'])->name('downloadQrCode');

    //Gestion PARAMETRAGE DUREE CARTE
    Route::resource('parametre', ParametreController::class)->except(['create', 'show']);

    //GESTION CARTE
    Route::resource('carte', CarteController::class);
    Route::post('/beneficiaire/vehicules', [CarteController::class, 'getVehicules'])->name('beneficiaire.vehicules');
    Route::get('/reactivation-cartes', [CarteController::class, 'reactivation_carte'])->name('reactivation');
    Route::get('ActivateCard/{user}', [CarteController::class, 'reactivate_card'])->name('cartes.activateState');
    Route::get('/desactivation-cartes', [CarteController::class, 'desactivation_carte'])->name('desactivation');
    Route::post('/desactiver-carte-commentaire/{id}', [CarteController::class, 'desactiverCarte'])->name('desactiver-carte');
    Route::post('/desactivate_card/{id}', [CarteController::class, 'desactivate_card'])->name('desactivate_card');
    Route::get('/approbation-cartes', [CarteController::class, 'approbation_carte'])->name('approbation');
    Route::get('changeStateApprovedCard/{user}', [CarteController::class, 'change_state_approved'])->name('cartes.update_state_approved');
    Route::get('impression', [ImpressionController::class, 'index'])->name('impression.index');
    Route::get('changeStateApprovedPrinterCard/{user}', [ImpressionController::class, 'change_state_approved_printer'])->name('cartes.update_state_approved_printer');

    //GESTION ASSIGNATION
    Route::post('/assigntAgentTerminal', [AssignationController::class, 'assign'])->name('assigntAgentTerminal');
    Route::resource('assignation', AssignationController::class)->except(['store', 'show', 'edit', 'update', 'destroy']);

    //GESTION DESASIGNATION
    Route::get('/desassignation', [DesassignationController::class, 'desassignationAgent'])->name('desassignation');
    Route::post('/desassignAgent/{id}', [DesassignationController::class, 'desassignAgent'])->name('desassignAgent');
    Route::post('/desaffecter-terminal/{id}', [DesassignationController::class, 'desaffecterTerminal'])->name('desaffecter.terminal');

    //GESTION AGENT
    Route::resource('agent', AgentController::class);
    Route::get('changeAgentState/{agent}', [AgentController::class, 'change_agent_state'])->name('change_agent_state');
    Route::put('changePasswordAgent/{id}', [AgentController::class, 'changePasswordAgent'])->name('changePasswordAgent');

    //GESTION UTILISATEUR WEB
    Route::resource('utilisateur', UserController::class)->except(['edit']);
    Route::post('/update_password/{id}/', [UserController::class, 'update_password'])->name('utilisateur.update_password');
    Route::get('/monProfile', [UserController::class, 'profilUser'])->name('monProfile');
    Route::put('/update_profile/{id}/', [UserController::class, 'updateProfile'])->name('update.profile');
    Route::put('/UpdateUserPassword/{id}/', [UserController::class, 'UpdateUserPassword'])->name('updatePasswordConnectedUser');
    Route::get('changeState/{user}', [UserController::class, 'change_state'])->name('change_state_user');
    Route::resource('profile', ProfileController::class)->except(['show']);

    //GESTION ZONE ET PORTE
    Route::resource('zone', ZoneController::class);
    Route::resource('porte', PorteController::class)->except(['index', 'edit', 'show', 'update']); //GESTION PORTE

    //GESTION STATUT TERMINAL
    Route::resource('StatutTerminal', StatutTerminalController::class)->except(['create', 'show',]);

    //GESTION FORME JURIDIQUE
    Route::resource('FormeJuridique', FormeJuridiqueController::class)->except(['create', 'show']);

    //GESTION TYPE VEHICULE
    Route::resource('TypeVehicule', TypeVehiculeController::class)->except(['create', 'show']);

    //GESTION MARQUE TERMINAL
    Route::resource('marque', MarqueController::class)->except(['create', 'show']);

    //GESTION VEHICULE
    Route::get('/vehicule_edit/{idVehicule}/{idBeneficiaire}/edit', [VehiculeController::class, 'editVehicule'])->name('vehicule.edition'); //Afiche le formulaire de modification du véhicule
    Route::get('changeStateVehicule/{Vehicule}', [VehiculeController::class, 'change_vehicule_state'])->name('change_vehicule_state'); //Changer le statut du vehicule
    Route::put('/vehicule_update/{idVehicule}/{idBeneficiaire}', [VehiculeController::class, 'updateVehicule'])->name('vehicules.updated'); //Route de mise à jour du véhicule
    Route::resource('vehicule', VehiculeController::class)->except(['index', 'create', 'edit', 'show', 'update']);

    //GESTION BENEFICIAIRE ET VEHICULE
    Route::resource('beneficiaire', BeneficiaireController::class);
    Route::put('/update_marqueVoiture/{id}/', [MarqueVoitureController::class, 'updateMarqueVoiture'])->name('update.marquevoiture');
    Route::resource('marqueVoiture', MarqueVoitureController::class)->except(['create', 'show']);

    //GESTION TERMINAL
    Route::resource('terminal', TerminalController::class)->except(['show']);

    //GESTION DES RAPPORTS
    Route::resource('rapports', UserController::class);
    Route::get('/rapport-beneficiaire', [RapportController::class, 'TotalCarteBeneficiaire'])->name('total_carte_beneficiaire');

    //DECONNEXION
    Route::get('/se-deconnecter-de-mon-compte-client', [LoginController::class, 'logout'])->name('logout');
});
