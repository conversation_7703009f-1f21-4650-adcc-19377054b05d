<?php

namespace Database\Seeders;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class FormeJuridiqueSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */

    public function run(): void
    {
        //
        DB::table('forme_juridiques')->insert([
            ['NomFormeJuridique' => 'Entreprise Individuelle (EI)', 'user_id' => 1],
            ['NomFormeJuridique' => 'Entreprise Unipersonnelle à Responsabilité Limitée (EURL)', 'user_id' => 1],
            ['NomFormeJuridique' => 'Société à Responsabilité Limitée (SARL)', 'user_id' => 1],
            ['NomFormeJuridique' => 'Société Anonyme (SA)', 'user_id' => 1],
            ['NomFormeJuridique' => 'Société par Actions Simplifiée (SAS)', 'user_id' => 1],
            ['NomFormeJuridique' => 'Société par Actions Simplifiée Unipersonnelle (SASU)', 'user_id' => 1],
            ['NomFormeJuridique' => 'Société en Nom Collectif (SNC)', 'user_id' => 1],
            ['NomFormeJuridique' => 'Société Coopérative de Production (Scop)', 'user_id' => 1],
            ['NomFormeJuridique' => 'Société en Commandite par Actions (SCA)', 'user_id' => 1],
            ['NomFormeJuridique' => 'Société en Commandite Simple (SCS)', 'user_id' => 1],
            // Ajoutez d'autres marques au besoin
        ]);
        
    }   
}
