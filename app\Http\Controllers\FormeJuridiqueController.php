<?php

namespace App\Http\Controllers;

use App\Models\Beneficiaire;
use App\Models\FormeJuridique;
use Illuminate\Http\Request;
use MercurySeries\Flashy\Flashy;

class FormeJuridiqueController extends Controller
{
    public function index()
    {
        $FormesJuridiques = FormeJuridique::all();
        $FormesJuridiquesCount = FormeJuridique::whereNull('created_at')->count();
        return view('formejuridique.index', compact('FormesJuridiques', 'FormesJuridiquesCount'));
    }

    public function edit(string $id)
    {
        $formeJuridique = FormeJuridique::find($id);
        return view('formejuridique.edit', compact('formeJuridique'));
    }

    public function store(Request $request)
    {
        $this->validate($request, [
            'NomFormeJuridique' => 'required|min:2',
        ]);

        FormeJuridique::create([
            'NomFormeJuridique' => $request->NomFormeJuridique,
            'user_id' => auth()->user()->id
        ]);
        Flashy::primary('Enregistrement de la forme juridique effectué avec succès');
        return redirect()->route('FormeJuridique.index');
    }

    public function update(Request $request, string $id)
    {
        $this->validate($request, [
            'NomFormeJuridique' => 'required|min:2',
        ]);

        $formeJuridique = FormeJuridique::find($id);
        $formeJuridique->NomFormeJuridique = $request->NomFormeJuridique;
        $formeJuridique->save();

        Flashy::success('Modification de la forme juridique éffectuée avec succès');
        return redirect()->route('FormeJuridique.index');
    }

    public function destroy(String $id)
    {
        $beneficiaire = Beneficiaire::where('forme_juridique_id', $id)->get();
        if ($beneficiaire->count() == 0) {
            FormeJuridique::find($id)->delete();
            Flashy::warning('Suppression de la forme juridique éffectués avec succès');
            return back();
        } else {
            Flashy::error('Impossible, Forme juridique déjà utilisé');
            return back();
        }
    }
}
