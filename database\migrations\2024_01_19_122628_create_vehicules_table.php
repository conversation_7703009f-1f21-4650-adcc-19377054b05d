<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('vehicules', function (Blueprint $table) {
            $table->id();
            $table->string('Immatriculation');
            $table->string('Model');
            $table->string('Couleur');
            $table->string('Description', 100)->nullable();
            $table->boolean('Statut')->default(0);
            $table->string('info_pdf')->nullable();
            $table->unsignedBigInteger('marque_voiture_id')->index();
            $table->foreign('marque_voiture_id')->references('id')->on('marque_voitures')->onDelete('cascade')->onUpdate('cascade');
            $table->unsignedBigInteger('type_vehicule_id')->index();
            $table->foreign('type_vehicule_id')->references('id')->on('type_vehicules')->onDelete('cascade')->onUpdate('cascade');
            $table->unsignedBigInteger('beneficiaire_id')->index();
            $table->foreign('beneficiaire_id')->references('id')->on('beneficiaires')->onDelete('cascade')->onUpdate('cascade');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('vehicules');
    }
};
