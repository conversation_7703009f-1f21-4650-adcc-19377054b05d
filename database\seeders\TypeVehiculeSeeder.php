<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class TypeVehiculeSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        //
        DB::table('type_vehicules')->insert([
            ['NomTypeVehicule' => 'Berline'],
            ['NomTypeVehicule' => 'SUV (Véhicule utilitaire sport)'],
            ['NomTypeVehicule' => 'Camionnette'],
            ['NomTypeVehicule' => 'Coupé'],
            ['NomTypeVehicule' => 'Cabriolet'],
            ['NomTypeVehicule' => 'Monospace'],
            ['NomTypeVehicule' => 'Break'],
            ['NomTypeVehicule' => 'Camion'],
            ['NomTypeVehicule' => 'Crossover'],
            ['NomTypeVehicule' => 'Pick-up'],
            ['NomTypeVehicule' => 'Roadster'],
            ['NomTypeVehicule' => 'Voiture électrique'],
            ['NomTypeVehicule' => 'Voiture hybride'],
            // Ajoutez d'autres marques au besoin
        ]);
    }
}
