$(document).ready(function () {

    $('#FormValidationPassword').validate({
        rules: {
            password: {
                required: true
                , strongPassword: true
            }
            , password_confirmation: {
                required: true
                , equalTo: "#password"
            }
        }
        , messages: {
            password: {
                required: "Mot de passe requis"
            }
            , password_confirmation: {
                required: "Confirmez le mot de passe"
                , equalTo: "Les mots de passe ne correspondent pas"
            }
        }
        , errorElement: 'span'
        , errorClass: 'text-danger'
        , highlight: function (element) {
            $(element).addClass('is-invalid');
        }
        , unhighlight: function (element) {
            $(element).removeClass('is-invalid');
        }
    });

});

$(document).ready(function () {
    function updateRule(selector, condition) {
        const li = $(selector);
        const icon = li.find(".icon");

        if (condition) {
            li.removeClass('invalid').addClass('valid');
            icon.removeClass('fa-circle-xmark').addClass('fa-circle-check');
        } else {
            li.removeClass('valid').addClass('invalid');
            icon.removeClass('fa-circle-check').addClass('fa-circle-xmark');
        }
    }

    // 💡 Mise à jour dynamique des règles pendant la saisie
    $('#password').on('keyup', function () {
        const val = $(this).val();

        updateRule('#rule-length', val.length >= 8);
        updateRule('#rule-uppercase', /[A-Z]/.test(val));
        updateRule('#rule-number', /[0-9]/.test(val));
        updateRule('#rule-special', /[\W_]/.test(val));
    });

    // 🔐 Méthode personnalisée : strongPassword
    $.validator.addMethod("strongPassword", function (value, element) {
        return this.optional(element) || (
            value.length >= 8 &&
            /[A-Z]/.test(value) &&
            /[0-9]/.test(value) &&
            /[\W_]/.test(value)
        );
    }, "Validation incomplète");


    // Méthode personnalisée : type de fichier
    $.validator.addMethod("filetype", function (value, element, param) {
        if (element.files.length === 0) return false;
        let fileType = element.files[0].type;
        return param.includes(fileType);
    }, "Seuls les fichiers JPG, JPEG ou PNG sont autorisés.");

    // Méthode personnalisée : taille de fichier max
    $.validator.addMethod("filesize", function (value, element, param) {
        if (element.files.length === 0) return false;
        return element.files[0].size <= param;
    }, "La taille du fichier ne doit pas dépasser 500 Ko.");

    // Méthode personnalisée : numéro guinéen
    $.validator.addMethod("guineaPhone", function (value, element) {
        return this.optional(element) || /^(620|621|622|623|624|625|626|627|628|629|610|611|612|613|660|661|662|663|664|665|666|654|655|656)\d{6}$/.test(value);
    }, "Numéro guinéen invalide");


    $('#togglePassword').on('click', function () {
        const passwordInput = $('#password');
        const type = passwordInput.attr('type') === 'password' ? 'text' : 'password';
        passwordInput.attr('type', type);

        // Basculer l’icône (œil / œil barré)
        $(this).toggleClass('fa-eye fa-eye-slash');
    });

    // Initialisation de la validation
    $('#FormValidationUser').validate({
        rules: {
            Nom: {
                required: true,
                minlength: 3
            },
            Prenom: {
                required: true,
                minlength: 3
            },
            Sexe: {
                required: true
            },
            Telephone: {
                required: true,
                digits: true,
                minlength: 9,
                maxlength: 9,
                guineaPhone: true
            },
            email: {
                required: true,
                email: true
            },
            Photo: {
                required: true,
                filetype: ["image/jpeg", "image/png", "image/jpg"],
                filesize: 512000 // 500 Ko
            },
            Profile: {
                required: true
            },
            password: {
                required: true,
                strongPassword: true
            },
            password_confirmation: {
                required: true,
                equalTo: "#password"
            }
        },
        messages: {
            Nom: {
                required: "Le nom est requis",
                minlength: "Minimum 3 caractères"
            },
            Prenom: {
                required: "Le prénom est requis",
                minlength: "Minimum 3 caractères"
            },
            Sexe: "Veuillez sélectionner un sexe",
            Telephone: {
                required: "Le téléphone est requis",
                digits: "Chiffres uniquement",
                minlength: "9 chiffres attendus",
                maxlength: "9 chiffres maximum"
            },
            email: {
                required: "L'email est requis",
                email: "Format invalide"
            },
            Photo: {
                required: "Une photo est requise"
            },
            Profile: "Sélectionnez un profil",
            password: {
                required: "Mot de passe requis"
            },
            password_confirmation: {
                required: "Confirmez le mot de passe",
                equalTo: "Les mots de passe ne correspondent pas"
            }
        },
        errorElement: 'span',
        errorClass: 'text-danger',
        highlight: function (element) {
            $(element).addClass('is-invalid');
        },
        unhighlight: function (element) {
            $(element).removeClass('is-invalid');
        }
    });

});

const btnEditPhoto = document.getElementById('btnEditPhoto');
const photoInput = document.getElementById('Photo');
const previewImage = document.getElementById('preview');

// Ouvrir l'explorateur quand on clique sur le bouton
btnEditPhoto.addEventListener('click', () => {
    photoInput.click();
});

// Afficher l'aperçu dès qu'une image est sélectionnée
photoInput.addEventListener('change', (event) => {
    const file = event.target.files[0];

    if (file) {
        const reader = new FileReader();
        reader.onload = function (e) {
            previewImage.src = e.target.result;
            previewImage.style.display = 'block';
        }
        reader.readAsDataURL(file);
    }
});
