@extends('layouts.base')
@section('content')
<div class="layout-px-spacing">
    <div class="middle-content container-xxl p-0">
        <!-- BREADCRUMB -->
        <div class="page-meta">
            <nav class="breadcrumb-style-one" aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item active" aria-current="page">RECHERCHER UN AGENT PAR SON CODE</li>
                </ol>
            </nav>
        </div>
        <!-- /BREADCRUMB -->
        <div class="widget-content widget-content-area custom-autocomplete h-100 mt-2">
            <div class="row">
                <div class="col-md-12 align-self-center order-md-0 order-1">
                    <div class="faq-header-content">
                        <div class="row">
                            <div class="col-lg-11">
                                <div class="autocomplete-btn">
                                    <form action="" method="GET" autocomplete="on">
                                        <input class="form-control" name="search" value="{{ old('search') }}" required>
                                        <small>Exemple code: OP000000</small>
                                        <button type="submit" class="btn btn-primary">Rechercher</button>
                                    </form>
                                </div>
                            </div>
                            @if(isset($results))
                            @if($results->isNotEmpty())
                            <div class="col-lg-1">
                                <a href="{{ route('assignation.create') }}" class="btn btn-link mt-2"><i class="fa fa-refresh"></i></a> <!-- Add refresh button -->
                            </div>
                            @endif
                            @endif
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- show agent informations  -->
        @if(isset($results))

        @if($results->isNotEmpty())
        @if(isset($error_alreadyTerminal))
        <div class="alert alert-danger mt-2" role="alert">
            {{ $error_alreadyTerminal }}
            <a href="{{ route('assignation.create') }}" class="btn btn-link btn-sm"><i class="fa fa-refresh"></i></a>
            <!-- Add refresh button -->
        </div>
        @else
        <div class="row layout-spacing ">
            <!-- Content -->
            <div class="col-xl-4 col-lg-4 col-md-12 col-sm-12 layout-top-spacing">
                <div class="user-profile">
                    <div class="widget-content widget-content-area">
                        <div class="d-flex justify-content-between">
                            <h3 class="text-center">Informations de l'agent</h3>
                        </div>
                        @foreach($results as $result)
                        <div class="text-center user-info">
                            <img src="{{ $result->Photo }}" alt="avatar" width="30%">
                            <p class=""><span>{{ $result->NomAgent }}</span> <span>{{ $result->PrenomAgent }}</span></p>
                        </div>
                        <div class="user-info-list">
                            <div class="">
                                <ul class="contacts-block list-unstyled">
                                    <li class="contacts-block__item">
                                        CODE: <b><span>{{ $result->CodeAgent }}</span></b>
                                    </li>
                                    <li class="contacts-block__item">
                                        PSEUDO: <b><span>{{ $result->Pseudo }}</span></b>
                                    </li>
                                    <li class="contacts-block__item">
                                        SEXE: <b><span>{{ $result->Sexe }}</span></b>
                                    </li>
                                    <li class="contacts-block__item">
                                        TELEPHONE: <b><span id="agentTelephone">{{ $result->Telephone }}</span></b>
                                    </li>

                                </ul>
                            </div>
                        </div>
                        @endforeach
                    </div>
                </div>
            </div>

            <div class="col-xl-8 col-lg-8 col-md-12 col-sm-12 layout-top-spacing">
                <div class="usr-tasks ">
                    <div class="widget-content widget-content-area">
                        <h3 class="">Affecter cet agent à un Terminal</h3>
                        <div class="table-responsive">
                            <table id="zero-config" class="table table-bordered">
                                <thead>
                                    <tr>
                                        <th>Nom Terminal</th>
                                        <th>N. Serie</th>
                                        <th>Statut</th>
                                        <th class="text-center">Affecter</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($terminaux as $terminal)
                                    @if($terminal->agent_id == null && $terminal->statut_terminal->Flag == 1)
                                    <tr>
                                        <td>{{ $terminal->NomTerminal }}</td>
                                        <td>
                                            {{ $terminal->NumeroSerie }}
                                        </td>
                                        <td>
                                            <span class="shadow-none badge badge-primary">{{
                                                $terminal->statut_terminal->NomStatut }}</span>
                                        </td>
                                        <td class="text-center">
                                            <form action="{{ route('assigntAgentTerminal') }}" method="POST" id="form{{$terminal->id}}">
                                                @csrf
                                                <!-- <input type="hidden" name="agentId" value="{{ $terminal->agent_id}}"> -->
                                                @foreach($results as $result)
                                                <input type="hidden" id="agentId" name="agent_ids[]" value="{{ $result->id }}">
                                                @endforeach
                                                <input type="hidden" name="terminal_id" value="{{ $terminal->id }}">
                                                <button type="button" data-original-title="Activer" data-toggle="tooltip" data-placement="top" onclick="affectAgentTerminal({{$terminal->id}})" class="btn btn-success btn-sm">Affecter</button>
                                            </form>

                                        </td>
                                    </tr>
                                    @endif
                                    @endforeach

                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

            </div>

        </div>
        @endif
        @else
        <!-- Display error message -->
        <div class="alert alert-danger mt-2" role="alert">
            {{ $error }}
            <a href="{{ route('assignation.create') }}" class="btn btn-link btn-sm"><i class="fa fa-refresh"></i></a>
            <!-- Add refresh button -->
        </div>
        @endif

        @endif


    </div>
</div>
@endsection
@section('js')
<script>
    function affectAgentTerminal(terminalId) {
        // Récupérer l'ID de l'agent recherché
        const form = document.querySelector(`#form${terminalId}`);
        // Récupérer le premier agent ID
        const agentId = form.querySelector('input[name="agent_ids[]"]').value;

        Swal.fire({
            title: 'Assignation'
            , html: `<strong style="color:#e2a03f">Vous êtes sur le point d'assigner le terminal à l'agent.</strong>`
            , icon: 'question'
            , showCancelButton: true
            , confirmButtonText: 'Oui, Affecter'
            , cancelButtonText: 'Annuler'
            , confirmButtonColor: '#f79102'
            , cancelButtonColor: '#87adbd'
            , customClass: {
                popup: 'shadow-lg rounded-lg'
                , confirmButton: 'btn btn-warning'
                , cancelButton: 'btn btn-secondary me-2'
            }
        }).then((result) => {
            if (result.isConfirmed) {
                // Remplir le champ agent_id avec l'ID de l'agent recherché
                form.querySelector('#agentId').value = agentId;
                // Soumettre le formulaire
                form.submit();
            }
        });
    }

</script>

@endsection
