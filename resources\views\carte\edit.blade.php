@extends('layouts.base')
@section('content')
<!-- BREADCRUMB -->
<div class="page-meta">
    <nav class="breadcrumb-style-one" aria-label="breadcrumb">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="{{ route('carte.index') }}">Liste des cartes</a></li>
            <li class="breadcrumb-item" aria-current="page"><a href="{{ route('carte.show', $carte->id) }}">Détail carte</a></li>
            <li class="breadcrumb-item active" aria-current="page">Ajout Zone</li>
        </ol>
    </nav>
</div>
<div class="layout-px-spacing">
    <div class="middle-content container-xxl p-0">
        <div class="row">
            <div id="flStackForm" class="col-lg-12 layout-spacing layout-top-spacing">
                <div class="statbox widget box box-shadow">
                    <div class="widget-header">
                        <div class="row">
                            <div class="col-xl-12 col-md-12 col-sm-12 col-12">
                                <h4>Ajout des zones d'acces pour la carte : {{ $carte->NumeroAutorisation }} du véhicule {{ $carte->vehicule->Immatriculation }}</h4>
                            </div>
                        </div>
                    </div>
                    <div class="widget-content widget-content-area">
                        <form id="mainForm" action="{{ route('carte.update', $carte->id) }}" method="POST" enctype="multipart/form-data">
                            @csrf
                            @method('PUT')
                            <!-- Affecter les portes d'accès -->
                            <div class="row mb-3">
                                <label for="selectVehicules" class="col-sm-2 col-form-label">Cochez les accès: </label>
                                @php
                                $portesCarte = json_decode($carte->Portes) ?? [];
                                @endphp
                                @foreach($zones as $zone)
                                <div class="col-md-3">
                                    <div class="card">
                                        <div class="card-header">{{ $zone->NomZone }}</div>
                                        <div class="card-body">
                                            @foreach($zone->porte as $porte)
                                            @php
                                            $checked = in_array($porte->id, $portesCarte) ? 'checked' : '';
                                            @endphp
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" name="portes[]" value="{{ $porte->id }}" id="porte_{{ $porte->id }}" {{ $checked }}>
                                                <label class="form-check-label" for="porte_{{ $porte->id }}">
                                                    {{ $porte->CodePorte }}
                                                </label>
                                            </div>
                                            @endforeach
                                        </div>
                                    </div>
                                </div>
                                @endforeach
                            </div>

                            <div class="col-sm-10 offset-sm-2 mt-3">
                                <a href="{{ route('carte.show', $carte->id) }}" class="btn btn-danger">Annuler</a>
                                <button type="submit" class="btn btn-dark">Modifier</button>
                            </div>
                        </form>
                    </div>

                </div>
            </div>

        </div>
    </div>
</div>
@endsection
@section('js')

<script>
    function updateVehicules() {
        var selectedBeneficiaire = document.getElementById('selectBeneficiaire').value;
        if (selectedBeneficiaire === "") {
            // Clear the vehicles dropdown if no beneficiary is selected
            document.getElementById('selectVehicules').innerHTML = '<option value="">Veuillez choisir un bénéficiaire d\'abord</option>';
        } else {
            document.getElementById('hiddenBeneficiaire').value = selectedBeneficiaire;
            document.getElementById('updateVehiculesForm').submit();
        }
    }

</script>





@endsection
