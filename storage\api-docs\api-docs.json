{"openapi": "3.0.0", "info": {"title": "Bienvenu dans la partie API", "version": "1.0.0"}, "paths": {"/api/loginAgent": {"post": {"tags": ["Agent <PERSON>"], "summary": "Connexion Agent", "description": "Permet à un agent de se connecter avec son pseudo, son mot de passe et l'identifiant Android de son terminal.", "operationId": "f9ef9df314d369a60e61c07348d3050c", "requestBody": {"required": true, "content": {"application/json": {"schema": {"required": ["pseudo", "password", "android_id", "first_connection"], "properties": {"pseudo": {"type": "string", "example": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "password": {"type": "string", "example": "123456789"}, "android_id": {"type": "string", "example": "abc123xyz456"}, "first_connection": {"type": "integer", "example": 0}}, "type": "object"}}}}, "responses": {"200": {"description": "Connexion réussie, retourne le <PERSON>", "content": {"application/json": {"schema": {"properties": {"statut": {"type": "integer", "example": 200}, "agent_id": {"type": "integer", "example": 1}, "token": {"type": "string", "example": "1|81XIED3beDzuQfTV3mT4xglvuOaN7VK3hldEO3NBlfabd17ba"}}, "type": "object"}}}}, "401": {"description": "Échec de la connexion : identifiants invalides, compte inactif ou terminal non assigné.", "content": {"application/json": {"schema": {"properties": {"statut": {"type": "integer", "example": 401}, "infoMessage": {"type": "string", "example": "Chers agent Ce terminal ne vous est pas affecté, veillez contacter votre administrateur"}}, "type": "object"}}}}, "500": {"description": "<PERSON>rreur interne du serveur", "content": {"application/json": {"schema": {"properties": {"statut": {"type": "integer", "example": 500}, "error": {"type": "string", "example": "<PERSON>é<PERSON> de l<PERSON>erreur"}}, "type": "object"}}}}}}}, "/api/logoutAgent": {"post": {"tags": ["Agent <PERSON>"], "summary": "Déconnexion Agent", "description": "Déconnecte l'agent en invalidant son token ou sa session.", "operationId": "15e667b42d59d00dbe7cbfc8a5bc9763", "responses": {"200": {"description": "Déconnexion réussie", "content": {"application/json": {"schema": {"properties": {"message": {"type": "string", "example": "Déconnexion réussie."}}, "type": "object"}}}}, "401": {"description": "Utilisateur non authentifié", "content": {"application/json": {"schema": {"properties": {"message": {"type": "string", "example": "Non authentifié."}}, "type": "object"}}}}}, "security": [{"bearerAuth": []}]}}, "/api/agentAuthData": {"get": {"tags": ["Data Auth"], "summary": "Info Agent Connecter", "description": "Permet de recuperer les informations concernant l'agent connecté", "operationId": "1f5236cfbb77d7791352bed4d6f06463", "responses": {"200": {"description": "<PERSON><PERSON><PERSON> reussie avec succès", "content": {"application/json": {"schema": {"properties": {"Status": {"type": "integer", "example": "200"}}, "type": "object"}}}}, "401": {"description": "Utilisateur non authentifié", "content": {"application/json": {"schema": {"properties": {"message": {"type": "string", "example": "Agent non authentifié."}}, "type": "object"}}}}, "500": {"description": "<PERSON><PERSON><PERSON> du <PERSON>", "content": {"application/json": {"schema": {"properties": {"statut": {"type": "integer", "example": "500"}}, "type": "object"}}}}}, "security": [{"bearerAuth": []}]}}, "/api/scanVehicule/{qrCode}": {"get": {"tags": ["<PERSON><PERSON>"], "summary": "Decryptage qrcode carte", "description": "Permet de decrypté le qrCode de la carte", "operationId": "80433d572a13a46f5e8ab3b10d956aaf", "parameters": [{"name": "Code Qr", "in": "path", "description": "CodeQr", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"required": ["android_id", "agent_id", "CodePorte"], "properties": {"android_id": {"type": "string", "example": "2DJJSJAL"}, "agent_id": {"type": "integer", "example": 1}, "CodePorte": {"type": "string", "example": "PORTE-OUEST"}}, "type": "object"}}}}, "responses": {"200": {"description": "Decryptage effectué avec succès", "content": {"application/json": {"schema": {"properties": {"Status": {"type": "integer", "example": "200"}}, "type": "object"}}}}, "401": {"description": "Utilisateur non authentifié", "content": {"application/json": {"schema": {"properties": {"message": {"type": "string", "example": "Agent non authentifié."}}, "type": "object"}}}}, "500": {"description": "<PERSON><PERSON><PERSON> du <PERSON>", "content": {"application/json": {"schema": {"properties": {"statut": {"type": "integer", "example": "500"}}, "type": "object"}}}}}, "security": [{"bearerAuth": []}]}}}, "components": {"securitySchemes": {"bearerAuth": {"type": "http", "description": "Utiliser un token Bearer pour l'authentification", "name": "Authorization", "in": "header", "bearerFormat": "JWT", "scheme": "bearer"}}}, "tags": [{"name": "Agent <PERSON>", "description": "Agent <PERSON>"}, {"name": "Data Auth", "description": "Data Auth"}, {"name": "<PERSON><PERSON>", "description": "<PERSON><PERSON>"}]}