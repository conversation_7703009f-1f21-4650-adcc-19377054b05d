<?php

namespace App\Http\Controllers;

use App\Models\MarqueVoiture;
use App\Models\Vehicule;
use Illuminate\Http\Request;
use MercurySeries\Flashy\Flashy;

class MarqueVoitureController extends Controller
{
    public function index()
    {
        $marquesVoiture = MarqueVoiture::all();
        $marquesVoitureCount = MarqueVoiture::whereNull('created_at')->count();
        return view('marqueVoiture.index', compact('marquesVoiture', 'marquesVoitureCount'));
    }

    public function store(Request $request)
    {
        $this->validate($request, [
            'NomMarque' => 'required|min:2',

        ]);

        MarqueVoiture::create([
            'NomMarque' => $request->NomMarque,
            'user_id' => auth()->user()->id
        ]);
        Flashy::primary("Marque voiture enregistrée avec succès");
        return redirect()->route('marqueVoiture.index');
    }

    public function edit(string $id)
    {
        $marque = MarqueVoiture::find($id);
        return view('marqueVoiture.edit', compact('marque'));
    }

    public function updateMarqueVoiture(Request $request, $id)
    {
        $this->validate($request, [
            'NomMarque' => 'required|min:2',
        ]);

        $marqueVoiture = MarqueVoiture::find($id);
        $marqueVoiture->NomMarque = $request->NomMarque;
        $marqueVoiture->save();

        Flashy::success("Marque voiture modifiée avec succès");
        return redirect()->route('marqueVoiture.index');
    }

    public function destroy(string $id)
    {
        $vehicule = Vehicule::where('marque_voiture_id', $id)->get();
        if ($vehicule->count() == 0) {
            MarqueVoiture::find($id)->delete();
            Flashy::warning("Marque voiture supprimer avec succès");
            return back();
        } else {
            Flashy::error("Impossible, Marque voiture déjà utilisé");
            return back();
        }
    }
}
