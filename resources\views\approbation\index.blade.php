@extends('layouts.base')
@section('content')
<div class="layout-px-spacing">
    <div class="middle-content container-xxl p-0">
        <div id="tableCustomBasic" class="col-lg-12 col-12 layout-spacing">
            <div class="d-flex justify-content-between align-items-center flex-wrap mb-4 mt-5 p-3 rounded shadow-sm bg-white border">
                <h2 class="fw-bold mb-0 text-dark">Approbation cartes</h2>
            </div>
            <div class="statbox widget box box-shadow">
                <div class="widget-content widget-content-area">
                    <table id="zero-config" class="table style-1 dt-table-hover non-hover">
                        <thead>
                            <tr>
                                <th scope="col">Numero carte</th>
                                <th scope="col">Immat Véhicule</th>
                                <th scope="col">Date Approbation</th>
                                <th scope="col">Date Expiration</th>
                                <th scope="col">Bénéficiaire</th>
                                <th scope="col">Statut d'approbation</th>
                                <th scope="col">Action</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($cartes as $carte)
                            <tr>
                                <td>{{ $carte->NumeroAutorisation}}</td>
                                <td>{{ $carte->vehicule->Immatriculation}}</td>
                                <td>@if($carte->DateApprobation)
                                    {{ \Carbon\Carbon::parse($carte->DateApprobation)->format('d-m-Y') }}@else -
                                    @endif</td>
                                <td>
                                    @if($carte->DateExpiration)
                                    {{ \Carbon\Carbon::parse($carte->DateExpiration)->format('d-m-Y') }}
                                    @else -
                                    @endif</td>
                                <td>{{ $carte->vehicule->beneficiaire->RaisonSociale}}</td>
                                <td>
                                    @if($carte->StatutApprobation == 0)
                                    <span class="badge badge-light-danger rounded fs-12px d-inline-flex align-items-center"><i class="fa fa-circle fs-9px fa-fw me-5px"></i>Non Approuvé</span>
                                    @elseif($carte->StatutApprobation == 1)
                                    <span class="badge badge-light-success bg-opacity-15 px-2 py-6px rounded fs-12px d-inline-flex align-items-center"><i class="fa fa-circle fs-9px fa-fw me-5px"></i>Approuvé</span>
                                    @else
                                    <span class="badge badge-light-secondary bg-opacity-15 px-2 py-6px rounded fs-12px d-inline-flex align-items-center"><i class="fa fa-circle fs-9px fa-fw me-5px"></i>Carte Imprimée</span>
                                    @endif
                                </td>

                                <td>
                                    <form action="{{ route('cartes.update_state_approved', $carte->id) }}" method="GET" id="submit{{ $carte->id}}">
                                        @if($carte->StatutApprobation == 0)
                                        <button type="button" class="btn btn-outline-success w-50" data-original-title="Activer" title="Approuver la carte" data-toggle="tooltip" data-placement="top" onclick="changeAction('{{ $carte->id }}', event)"><i class="fa fa-check" aria-hidden="true"></i></button>
                                        @elseif($carte->StatutApprobation == 1)
                                        <button type="button" class="btn btn-outline-danger w-50" data-original-title="Desactiver" title="Desapprouver la carte" data-toggle="tooltip" data-placement="top" onclick="changeAction('{{ $carte->id }}', event)"><i class="fa fa-ban" aria-hidden="true"></i></button>
                                        @elseif(Auth::user()->isAdmin())
                                        <button type="button" class="btn btn-outline-secondary w-50" data-original-title="Desactiver" title="Reapprouver la carte" data-toggle="tooltip" data-placement="top" onclick="changeAction('{{ $carte->id }}', event)"><i class="fa fa-refresh" aria-hidden="true"></i></button>
                                        @endif
                                    </form>
                                </td>
                            </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
@section('js')
<script type="text/javascript">
    // Fonction JS corrigée
    function changeAction(id, event) {
        event.preventDefault(); // fonctionne maintenant ✅
        const form = document.querySelector(`#submit${id}`);

        Swal.fire({
            title: 'Changement statut approbation'
            , html: `<strong style="color:#e2a03f">Vous êtes sur le point de changer le statut d'approbation de la carte.</strong>`
            , icon: 'question'
            , showCancelButton: true
            , confirmButtonText: 'Oui, changer le statut'
            , cancelButtonText: 'Annuler'
            , confirmButtonColor: '#f79102'
            , cancelButtonColor: '#87adbd'
            , customClass: {
                popup: 'shadow-lg rounded-lg'
                , confirmButton: 'btn btn-warning'
                , cancelButton: 'btn btn-secondary me-2'
            }
        }).then((result) => {
            if (result.isConfirmed) {
                form.submit();
            }
        });
    }

</script>
@endsection
