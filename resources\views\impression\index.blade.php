@extends('layouts.base')
@section('content')
<div class="layout-px-spacing">
    <div class="middle-content container-xxl p-0">
        <div id="tableCustomBasic" class="col-lg-12 col-12 layout-spacing">
            <div class="d-flex justify-content-between align-items-center flex-wrap mb-4 mt-5 p-3 rounded shadow-sm bg-white border">
                <h2 class="fw-bold mb-0 text-dark">Impression cartes</h2>
            </div>
            <div class="statbox widget box box-shadow">
                <div class="widget-content widget-content-area">
                    <table id="zero-config" class="table style-1 dt-table-hover non-hover">
                        <thead>
                            <tr>
                                <th class="" scope="col">Numero carte</th>
                                <th class="text-center" scope="col">Immatriculation Véhicule</th>
                                <th class="text-center" scope="col">Nom Bénéficiaire</th>
                                <th class="text-center" scope="col">Statut Carte</th>
                                <th class="text-center" scope="col">Action</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($cartes as $carte)
                            <tr>
                                <td>{{ $carte->NumeroAutorisation}}</td>
                                <td class="text-center">{{ $carte->vehicule->Immatriculation}}</td>
                                <td class="text-center">{{ $carte->vehicule->beneficiaire->RaisonSociale}}</td>
                                <td class="text-center">
                                    <span class="badge badge-light-secondary bg-opacity-15 px-2 py-6px rounded fs-12px d-inline-flex align-items-center"><i class="fa fa-circle fs-9px fa-fw me-5px"></i>En attente d'impression</span>
                                </td>

                                <td class="text-center">
                                    <form action="{{ route('cartes.update_state_approved_printer', $carte->id) }}" method="GET" id="submit{{ $carte->id}}">
                                        <button type="button" class="btn btn-outline-danger btn-success" title="Valider l'impression" onclick="changeAction('{{ $carte->id}}')">
                                            <i class="fa fa-check"></i>
                                        </button>
                                    </form>
                                </td>
                            </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
@section('js')
<script type="text/javascript">
    function changeAction(id) {
        let form = document.querySelector(`#submit${id}`);
        Swal.fire({
            title: '<span style="color:#d32f2f; font-size:24px; font-weight:bold;">⚠️ Confirmation d\'impression</span>'
            , html: `
        <div style="font-size:16px; line-height:1.6;">
            <p><strong>Êtes-vous certain de vouloir valider la bonne impression de la carte ?</strong></p>
            <p style="color:#b71c1c;">Cette action va faire disparaitre la carte dans votre interface, avant de confirmer assurez vous que la qualité d'impression est bonne</p>
        </div>
    `
            , icon: 'error'
            , showCancelButton: true
            , confirmButtonText: '<i class="fas fa-check"></i> Oui, supprimer'
            , cancelButtonText: '<i class="fas fa-times"></i> Annuler'
            , confirmButtonColor: '#e53935'
            , cancelButtonColor: '#9e9e9e'
            , background: '#fff'
            , customClass: {
                popup: 'shadow border border-danger rounded-3 px-4 py-3'
                , confirmButton: 'btn btn-danger px-4 py-2'
                , cancelButton: 'btn btn-outline-secondary px-4 py-2'
            }
            , backdrop: `
        rgba(0,0,0,0.4)
        left top
        no-repeat
    `
        }).then((result) => {
            if (result.isConfirmed) {
                form.submit();
            }
        });
    }

</script>
@endsection
