<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Crypt;
use Illuminate\Support\Facades\DB;

class PorteSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        DB::table('portes')->insert([
            ['CodePorte' => 'Porte SUD-OUEST', 'Description' => 'SUD OUEST', 'zone_id' => 1, 'qrCode' => Crypt::encryptString("Porte SUD-OUEST")],
            ['CodePorte' => 'Porte NORD-OUEST', 'Description' => 'NORD OUEST', 'zone_id' => 1, 'qrCode' => Crypt::encryptString("Porte NORD-OUEST")],
            ['CodePorte' => 'Porte EST-OUEST', 'Description' => 'EST OUEST', 'zone_id' => 2, 'qrCode' => Crypt::encryptString("Porte EST-OUEST")],
            ['CodePorte' => 'Porte NORD-SUD', 'Description' => 'NORD SUD', 'zone_id' => 2, 'qrCode' => Crypt::encryptString("Porte NORD-SUD")],
            ['CodePorte' => 'Porte SUD-NORD', 'Description' => 'SUD NORD', 'zone_id' => 3, 'qrCode' => Crypt::encryptString("Porte SUD-NORD")],
            ['CodePorte' => 'Porte SUD-EST', 'Description' => 'SUD EST', 'zone_id' => 3, 'qrCode' => Crypt::encryptString("Porte SUD-EST")],
        ]);

        DB::table('parametres')->insert([
            [
                'NombreJoursExpiration' => 30,
                'user_id' => 1
            ]
        ]);
    }
}
