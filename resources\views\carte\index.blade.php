@extends('layouts.base')
@section('content')
<div class="layout-px-spacing">
    <div class="middle-content container-xxl p-0">
        <div id="tableCustomBasic" class="col-lg-12 col-12 layout-spacing">
            <div class="d-flex justify-content-between align-items-center flex-wrap mb-4 mt-5 p-3 rounded shadow-sm bg-white border">
                <h2 class="fw-bold mb-0 text-dark">Liste des cartes générées</h2>
                <a href="{{ route('carte.create') }}" class="btn btn-primary d-flex align-items-center">
                    <i class="fa fa-plus me-2"></i> Générer
                </a>
            </div>
            <div class="statbox widget box box-shadow">
                <div class="widget-content widget-content-area">
                    <table id="zero-config" class="table style-1 dt-table-hover non-hover table-responsive">
                        <thead>
                            <tr>
                                <th class="">Numero carte</th>
                                <th class="text-center">Date Approbation</th>
                                <th class="text-center">Date Expiration</th>
                                <th class="text-center">Immatriculation Véhicule</th>
                                <th class="text-center">Nom Bénéficiaire</th>
                                <th class="text-center">Detail carte</th>
                                <th class="text-center">Annulation carte</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($cartes as $carte)
                            <tr>
                                <td class="">{{ $carte->NumeroAutorisation}}</td>
                                <td class="text-center">@if($carte->DateApprobation)
                                    {{ \Carbon\Carbon::parse($carte->DateApprobation)->format('d-m-Y') }}@else -
                                    @endif</td>
                                <td class="text-center">@if($carte->DateExpiration)
                                    {{ \Carbon\Carbon::parse($carte->DateExpiration)->format('d-m-Y') }}@else -
                                    @endif</td>
                                <td class="text-center">{{ $carte->vehicule->Immatriculation}}</td>
                                <td class="text-center">{{ $carte->vehicule->beneficiaire->RaisonSociale}}</td>
                                <td class="text-center">
                                    <div class="action-btns">
                                        <a href="{{ route('carte.show', $carte->id) }}" class="action-btn btn-view bs-tooltip me-2" data-toggle="tooltip" data-placement="top" title="Detail Carte">
                                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-eye">
                                                <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"></path>
                                                <circle cx="12" cy="12" r="3"></circle>
                                            </svg>
                                        </a>
                                    </div>
                                </td>
                                <td class="text-center">
                                    @if ($carte->StatutApprobation == 0)
                                    <form action="{{ route('carte.destroy', $carte->id) }}" method="POST">
                                        @csrf
                                        @method('DELETE')
                                        <button type="submit" class="btn btn-outline-danger show_confirm" title='Annuler la carte'>
                                            <i class="fas fa-cancel"></i>
                                        </button>
                                    </form>
                                    @endif
                                </td>
                            </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
@section('js')
<script type="text/javascript">
    $(document).ready(function() {
        $('.show_confirm').click(function(event) {
            var form = $(this).closest("form");
            var name = $(this).data("name");
            event.preventDefault();
            Swal.fire({
                title: '<span style="color:#d32f2f; font-size:24px; font-weight:bold;">⚠️ Annulation carte</span>'
                , html: `
        <div style="font-size:16px; line-height:1.6;">
            <p><strong>Êtes-vous certain de vouloir annuler cette carte</strong></p>
            <p style="color:#b71c1c;">Cette action <u>ne pourra pas être annulée</u> !</p>
        </div>
    `
                , icon: 'error'
                , showCancelButton: true
                , confirmButtonText: '<i class="fas fa-check"></i> Oui, supprimer'
                , cancelButtonText: '<i class="fas fa-times"></i> Annuler'
                , confirmButtonColor: '#e53935'
                , cancelButtonColor: '#9e9e9e'
                , background: '#fff'
                , customClass: {
                    popup: 'shadow border border-danger rounded-3 px-4 py-3'
                    , confirmButton: 'btn btn-danger px-4 py-2'
                    , cancelButton: 'btn btn-outline-secondary px-4 py-2'
                }
                , backdrop: `
        rgba(0,0,0,0.4)
        left top
        no-repeat
    `
            }).then((result) => {
                if (result.isConfirmed) {
                    form.submit();
                }
            });
        });
    });

</script>
@endsection
