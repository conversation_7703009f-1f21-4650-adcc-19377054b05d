@extends('layouts.base')
@section('content')
<div class="layout-px-spacing">
    <div class="middle-content container-xxl p-0">
        <!-- BREADCRUMB -->
        <div class="page-meta">
            <nav class="breadcrumb-style-one" aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">Tableau de bord</a></li>
                    <li class="breadcrumb-item active" aria-current="page">Parametres</li>
                </ol>
            </nav>
        </div>
        <!-- /BREADCRUMB -->
        <div class="account-settings-container layout-top-spacing">
            <div class="account-content">
                <div class="row mb-3">
                    <div class="col-md-12">

                        <ul class="nav nav-pills" id="animateLine" role="tablist">
                            <li class="nav-item" role="presentation">
                                <button class="nav-link active" id="animated-underline-home-tab" data-bs-toggle="tab" href="#animated-underline-home" role="tab" aria-controls="animated-underline-home" aria-selected="true"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-home">
                                        <path d="M3 9l9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"></path>
                                        <polyline points="9 22 9 12 15 12 15 22"></polyline>
                                    </svg> Parametre de l'application</button>
                            </li>

                        </ul>
                    </div>
                </div>

                <div class="tab-content" id="animateLineContent-4">
                    <div class="tab-pane fade show active" id="animated-underline-home" role="tabpanel" aria-labelledby="animated-underline-home-tab">
                        <div class="row">
                            <div class="col-xl-12 col-lg-12 col-md-12 layout-spacing">
                                <form method="POST" action="{{ route('parametre.store')}}" enctype="multipart/form-data" class="section general-info">
                                    @csrf
                                    <div class="info">
                                        <div class="row">
                                            <div class="col-lg-11 mx-auto">
                                                <div class="row">

                                                    <div class="col-xl-10 col-lg-12 col-md-8 mt-md-0 mt-4">
                                                        <div class="form">
                                                            <div class="row">
                                                                <div class="col-md-10">
                                                                    <label for="Nom">Durée de validité des cartes (JOUR)</label>
                                                                    <div class="input-group mb-3">
                                                                        <input type="number" class="form-control" min="0" id="NombreJoursExpiration" name="NombreJoursExpiration" placeholder="Saisissez le nombre de jour">
                                                                        <button class="btn btn-primary" type="submit" id="button-addon2">Enregistrer</button>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </form>
                            </div>
                        </div>
                        <div class="col-xl-12 col-lg-12 col-md-12 layout-spacing">
                            <div class="info">
                                <div class="row">
                                    <table id="zero-config" class="table style-1 dt-table-hover non-hover">
                                        <thead>
                                            <tr>
                                                <th class="" scope="col">Nombre de jour</th>
                                                <th class="text-center" scope="col">Modifier</th>
                                                <th class="text-center" scope="col">Supprimer</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @foreach($NombreJoursExpiration as $nombreA)
                                            <tr>
                                                <td>
                                                    {{ $nombreA->NombreJoursExpiration }} Jour(s)
                                                </td>
                                                <td class="text-center">
                                                    <div class="action-btns">
                                                        <a href="{{ route('parametre.edit', $nombreA->id) }}" class="btn btn-primary btn-xs">
                                                            <i class="fa fa-edit"></i>
                                                        </a>
                                                    </div>
                                                </td>
                                                <td class="text-center">
                                                    <form method="POST" action="{{ route('parametre.destroy', $nombreA->id) }}">
                                                        @csrf
                                                        @method('DELETE')
                                                        <button type="submit" class="btn btn-xs btn-outline-danger btn-default show_confirm" title='Suppression'><i class="fa fa-times-circle"></i></button>
                                                    </form>
                                                </td>
                                            </tr>
                                            @endforeach
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="tab-pane fade" id="animated-underline-profile" role="tabpanel" aria-labelledby="animated-underline-profile-tab">
                        <div class="row">
                            <div class="col-xl-6 col-lg-12 col-md-12 mx-auto layout-spacing ">
                                <div class="section general-info payment-info ">
                                    <div class="info">
                                        <h6 class="">Modifier mes informations de connexion</h6>
                                        <p>Reinitialisé votre <span class="text-success">Mot de Passe Facilement</span></p>
                                    </div>
                                </div>
                            </div>

                        </div>
                    </div>

                    <div class="tab-pane fade" id="animated-underline-preferences" role="tabpanel" aria-labelledby="animated-underline-preferences-tab">
                        <div class="row">
                            <div class="col-xl-12 col-lg-12 col-md-12 layout-spacing">
                                <div class="section general-info">
                                    <div class="info">
                                        <h6 class="">Choose Theme</h6>
                                        <div class="d-sm-flex justify-content-around">
                                            <div class="form-check form-check-inline">
                                                <input class="form-check-input" type="radio" name="flexRadioDefault" id="flexRadioDefault1" checked>
                                                <label class="form-check-label" for="flexRadioDefault1">
                                                    <img class="ms-3" width="100" height="68" alt="settings-dark" src="../src/assets/img/settings-light.svg">
                                                </label>
                                            </div>

                                            <div class="form-check form-check-inline">
                                                <input class="form-check-input" type="radio" name="flexRadioDefault" id="flexRadioDefault2">
                                                <label class="form-check-label" for="flexRadioDefault2">
                                                    <img class="ms-3" width="100" height="68" alt="settings-light" src="../src/assets/img/settings-dark.svg">
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@endsection
@section('js')
<script type="text/javascript">
    $('.show_confirm').click(function(event) {
        var form = $(this).closest("form");
        var name = $(this).data("name");
        event.preventDefault();
        Swal.fire({
            title: '<span style="color:#d32f2f; font-size:24px; font-weight:bold;">⚠️ Suppression définitive</span>'
            , html: `
        <div style="font-size:16px; line-height:1.6;">
            <p><strong>Êtes-vous certain de vouloir supprimer le paramètre enregistré ?</strong></p>
            <p style="color:#b71c1c;">Cette action <u>ne pourra pas être annulée</u> !</p>
        </div>
    `
            , icon: 'error'
            , showCancelButton: true
            , confirmButtonText: '<i class="fas fa-check"></i> Oui, supprimer'
            , cancelButtonText: '<i class="fas fa-times"></i> Annuler'
            , confirmButtonColor: '#e53935'
            , cancelButtonColor: '#9e9e9e'
            , background: '#fff'
            , customClass: {
                popup: 'shadow border border-danger rounded-3 px-4 py-3'
                , confirmButton: 'btn btn-danger px-4 py-2'
                , cancelButton: 'btn btn-outline-secondary px-4 py-2'
            }
            , backdrop: `
        rgba(0,0,0,0.4)
        left top
        no-repeat
    `
        }).then((result) => {
            if (result.isConfirmed) {
                form.submit();
            }
        });
    });

</script>
@endsection
