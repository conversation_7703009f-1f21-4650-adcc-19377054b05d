<?php

namespace App\Http\Controllers;

use App\Models\StatutTerminal;
use App\Models\Terminal;
use Illuminate\Http\Request;
use MercurySeries\Flashy\Flashy;

class StatutTerminalController extends Controller
{
    public function index()
    {
        $statutsTerminaux = StatutTerminal::all();
        $statutsTerminauxCount = StatutTerminal::whereNull('created_at')->count();
        return view('statutTerminal.index', compact('statutsTerminaux', 'statutsTerminauxCount'));
    }

    public function store(Request $request)
    {
        $this->validate($request, [
            'NomStatut' => 'required|min:2',
        ]);
        //Récupération du dernier statut terminal
        $dernierStatut = StatutTerminal::orderBy('id', 'desc')->first();
        //Récupération du dernier Flag
        $dernierFlag = $dernierStatut->Flag;
        //Incrementer le nouveau flag au dernier flag + 1
        $nouveauFlag = $dernierFlag + 1;
        //Création du nouveau statut terminal avec le nouveau flag incrémenté
        $nouveauStatut = new StatutTerminal();
        $nouveauStatut->NomStatut = $request->NomStatut;
        $nouveauStatut->Flag = $nouveauFlag;
        $nouveauStatut->user_id = auth()->user()->id;
        $nouveauStatut->save();
        Flashy::primary("Statut terminal ajouté avec succès");
        return redirect()->route('StatutTerminal.index');
    }

    public function edit(string $id)
    {
        $statutTerminal = StatutTerminal::find($id);
        return view('statutTerminal.edit', compact('statutTerminal'));
    }

    public function update(Request $request, string $id)
    {
        $this->validate($request, [
            'NomStatut' => 'required|min:2',
        ]);

        $statutTerminal = StatutTerminal::find($id);
        $statutTerminal->NomStatut = $request->NomStatut;
        $statutTerminal->save();

        Flashy::success("Statut terminal modifié avec succès");
        return redirect()->route('StatutTerminal.index');
    }

    public function destroy(String $id)
    {
        $terminal = Terminal::where('statut_terminal_id', $id)->get();

        if ($terminal->count() == 0) {
            StatutTerminal::find($id)->delete();
            Flashy::warning("Statut terminal supprimé avec succès");
            return back();
        } else {
            Flashy::error("Impossible, statut terminal déjà utilisé");
            return back();
        }
    }
}
