@extends('layouts.base')
@section('content')
<div class="d-flex justify-content-between align-items-center flex-wrap mb-4 mt-5 p-3 rounded shadow-sm bg-white border">
    <h2 class="fw-bold mb-0 text-dark">D<PERSON><PERSON> de la carte {{ $carte->NumeroAutorisation }} du véhicule {{ $carte->vehicule->Immatriculation }}</h2>
</div>
<div class="page-meta mb-4">
    <nav class="breadcrumb-style-one" aria-label="breadcrumb">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="{{ route('carte.index') }}">Cartes</a></li>
            <li class="breadcrumb-item active" aria-current="page">Détails de la carte</li>
        </ol>
    </nav>
</div>
<div class="row">
    <div class="col-xxl-12 col-xl-12 col-lg-12 col-md-12 col-sm-12 mb-4">
        <div class="widget-content widget-content-area br-8 text-white" style="background: linear-gradient(to right, #afeeee, #4682b4), #f0f0f0; position: relative;">
            <div class="row justify-content-center">
                <div class="col-xxl-5 col-xl-6 col-lg-7 col-md-7 col-sm-9 col-12 pe-3">
                    <!-- Swiper -->
                    <div id="main-slider" class="splide">
                        <div class="splide__track">
                            {{ QrCode::size(400)->backgroundColor(0, 0, 0,
                            0)->generate($carte->CodeQR) }}
                        </div>
                    </div>
                </div>
                <div class="col-xxl-4 col-xl-5 col-lg-12 col-md-12 col-12 mt-xl-0 mt-5 align-self-center">
                    <div class="product-details-content">
                        <h3 class="product-title mt-4 h4">Nom du beneficiaire:
                            {{ $carte->vehicule->beneficiaire->RaisonSociale }}</h3>
                        <hr class="mb-2">
                        <div class="row color-swatch mb-2">
                            <div class="col-xl-6 col-lg-6 col-sm-6 align-self-center"><b>Numero
                                    d'autorisation</b>
                            </div>
                            <div class="col-xl-6 col-lg-6 col-sm-6">
                                <div class="color-options text-xl-end">
                                    <b>{{ $carte->NumeroAutorisation }}</b>
                                </div>
                            </div>
                        </div>
                        <div class="row size-selector mb-2">
                            <div class="col-xl-6 col-lg-6 col-sm-6 align-self-center"><b>Date
                                    Délivrance:</b></div>
                            <div class="col-xl-6 col-lg-6 col-sm-6 text-xl-end">
                                <b> {{ \Carbon\Carbon::parse($carte->DateDelivrance)->format('d/m/Y') }}</b>
                            </div>
                        </div>
                        <div class="row color-swatch mb-3">
                            <div class="col-xl-6 col-lg-6 col-sm-6 align-self-center"><b>Date
                                    Expiration</b> </div>
                            <div class="col-xl-6 col-lg-6 col-sm-6">
                                <div class="color-options text-xl-end">
                                    <b>{{ \Carbon\Carbon::parse($carte->DateExpiration)->format('d/m/Y') }}</b>
                                </div>
                            </div>
                        </div>
                        <div class="row size-selector mb-2">
                            <div class="col-xl-12 col-lg-12 col-sm-12 text-center">
                                <b>LES PORTES D'ACCESS(ZONES)</b>
                                <a href="{{ route('carte.edit', $carte->id) }}"><i class="fas fa-edit" style="cursor: pointer; margin-left: 15px; color:#DCDCDC" title="Ajouter des zones d'accès"></i></a>
                            </div>
                        </div>
                        <div class="row">
                            @if (!is_null($carte->Portes) && !empty(json_decode($carte->Portes)))
                            @foreach (json_decode($carte->Portes) as $porteId)
                            <div class="col-xl-6 col-lg-6 col-sm-6">
                                <b>
                                    @php
                                    $porte = App\Models\Porte::find($porteId);
                                    @endphp
                                    <p style="color: #000">
                                        {{ $porte->CodePorte }}
                                        ({{ $porte->zone->NomZone }})
                                    </p>
                                </b>
                            </div>
                            @endforeach
                            @else
                            <div class="bg-warning"><span>Pas de porte d'accès défini pour
                                    cette
                                    carte, par defaut la carte est inactive même si son
                                    statut
                                    est
                                    activé</span></div>
                            @endif
                        </div>
                        <hr class="mb-3">
                        <div class="secure-info mt-4">
                            <p class="text-white">Priére de garder active la carte pour
                                permettre au
                                vehicule
                                d'acceder au parc</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
