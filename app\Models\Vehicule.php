<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Vehicule extends Model
{
    use HasFactory;

    protected $fillable = [
        'Immatriculation',
        'Model',
        'Couleur',
        'Description',
        'marque_voiture_id',
        'type_vehicule_id',
        'beneficiaire_id',
        'Statut',
        'info_pdf',
    ];

    public function beneficiaire()
    {
        return $this->belongsTo(Beneficiaire::class);
    }

    public function marqueVoiture()
    {
        return $this->belongsTo(MarqueVoiture::class);
    }
    public function type_vehicule()
    {
        return $this->belongsTo(TypeVehicule::class);
    }
    public function carte()
    {
        return $this->hasMany(Carte::class);
    }

    public function getPDFURLAttribute()
    {
        return asset('uploads/documents\\') . $this->info_pdf;
    }
}
