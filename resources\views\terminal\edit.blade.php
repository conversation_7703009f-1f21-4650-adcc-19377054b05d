@extends('layouts.base')
@section('content')
<x-breadcrumb :current="'Modification terminal'" previous="Terminals" :previousRoute="route('terminal.index')" />
<div class="layout-px-spacing">
    <div class="middle-content container-xxl p-0">
        <div class="row">
            <div id="flStackForm" class="col-lg-12 layout-spacing layout-top-spacing">
                <div class="statbox widget box box-shadow">
                    <div class="widget-header">
                        <div class="row">
                            <div class="col-xl-12 col-md-12 col-sm-12 col-12">
                                <h4>Modification de <b>{{ $terminal->NomTerminal }}</b> </h4>
                            </div>
                        </div>
                    </div>
                    <div class="widget-content widget-content-area">
                        <form id="FormValidationTerminal" action="{{ route('terminal.update', $terminal->id) }}" method="POST" enctype="multipart/form-data">
                            @csrf
                            @method('PUT')
                            <div class="mb-3 row">
                                <label for="NomTerminal" class="col-sm-2 col-form-label">Nom du terminal *</label>
                                <div class="col-sm-10">
                                    <input type="text" class="form-control text-black" id="NomTerminal" name="NomTerminal" value="{{  $terminal->NomTerminal }}">
                                </div>
                            </div>

                            <div class="mb-3 row">
                                <label for="AdresseMac" class="col-sm-2 col-form-label">Adresse Mac *</label>
                                <div class="col-sm-10">
                                    <input type="text" class="form-control" id="AdresseMac" name="AdresseMac" value="{{ $terminal->AdresseMac }}">
                                </div>
                            </div>

                            <div class="mb-3 row">
                                <label for="NumeroSerie" class="col-sm-2 col-form-label">Numero Serie *</label>
                                <div class="col-sm-10">
                                    <input type="text" class="form-control" id="NumeroSerie" name="NumeroSerie" value="{{ $terminal->NumeroSerie }}">
                                </div>
                            </div>

                            <div class="mb-3 row">
                                <label for="marque_id" class="col-sm-2 col-form-label">Marque Terminal *</label>
                                <div class="col-sm-10">
                                    <select class="form-select" id="marque_id" name="marque_id">
                                        @foreach($marques as $marque)
                                        <option value="{{ $marque->id}}" {{ $terminal->marque_id == $marque->id ?
                                            'selected' : '' }}>{{ $marque->NomMarque}}</option>
                                        @endforeach
                                    </select>
                                </div>
                            </div>
                            <div class="mb-3 row">
                                <label for="Model" class="col-sm-2 col-form-label">Model Terminal *</label>
                                <div class="col-sm-10">
                                    <input type="text" id="Model" name="Model" value="{{ $terminal->Model }}" class="form-control">
                                </div>
                            </div>
                            <div class="mb-3 row">
                                <label for="statut_terminal_id" class="col-sm-2 col-form-label">Statut Terminal *</label>
                                <div class="col-sm-10">
                                    <select class="form-select" id="statut_terminal_id" name="statut_terminal_id">
                                        @foreach($statutTerminals as $statutTerminal)
                                        <option value="{{ $statutTerminal->id}}" {{ $terminal->statut_terminal_id ==
                                            $statutTerminal->id ? 'selected' : '' }}>{{ $statutTerminal->NomStatut}}
                                        </option>
                                        @endforeach
                                    </select>
                                </div>
                            </div>

                            <div class="mb-3 row">
                                <label for="Description" class="col-sm-2 col-form-label">Description </label>
                                <div class="col-sm-10">
                                    <textarea type="text" class="form-control" id="Description" cols="8" rows="5" name="Description">{{ $terminal->Description }}
                                    </textarea>
                                </div>
                            </div>
                            <div class="form-group row">
                                <div class="col-sm-10 offset-sm-2 mt-3">
                                    <a href="{{ route('terminal.index') }}" class="btn btn-danger">Annuler</a>
                                    <button type="submit" class="btn btn-dark">Modifier le terminal</button>
                                </div>
                        </form>
                    </div>
                </div>
            </div>

        </div>
    </div>
</div>
@endsection
@push('scripts')
<script src="{{asset('static/src/assets/js/jsPage/form-create-terminal.js') }}"></script>
@endpush
