<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Carte extends Model
{
    use HasFactory;

    protected $fillable = [
        'NumeroAutorisation',
        'DateApprobation',
        'DateExpiration',
        'Statut',
        'StatutApprobation',
        'Commentaire',
        'Portes',
        'CodeQR',
        'vehicule_id',
        'user_id',
        'Commentaire_desactivation'
    ];

    public function vehicule()
    {
        return $this->belongsTo(Vehicule::class);
    }

    public function scans()
    {
        return $this->belongsTo(Scan::class);
    }
}
