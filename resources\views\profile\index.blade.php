@extends('layouts.base')
@section('content')
<div class="layout-px-spacing">
    <div class="middle-content container-xxl p-0">
        <div id="tableCustomBasic" class="col-lg-12 col-12 layout-spacing">
            <!-- Header -->
            <div class="d-flex justify-content-between align-items-center flex-wrap mb-4 mt-5 p-3 rounded shadow-sm bg-white border">
                <h2 class="fw-bold mb-0 text-dark">Liste des Profiles</h2>
                <a href="{{ route('profile.create') }}" class="btn btn-primary d-flex align-items-center">
                    <i class="fa fa-plus me-2"></i> Nouveau
                </a>
            </div>
            <div class="statbox widget box box-shadow">
                <div class="widget-content widget-content-area">
                    <table id="zero-config" class="table style-1 dt-table-hover non-hover">
                        <thead>
                            <tr>
                                <th scope="col">Rôle</th>
                                <th>Utilisateurs</th>
                                <th>Permissions</th>
                                <th>Type plateforme</th>
                                <th>Action</th>
                                <th>Suppression</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($profiles as $profile)
                            <tr>
                                <td>{{ $profile->name }} </td>
                                <td>
                                    @if($profile->StatutProfile == 'web')
                                    {{ $profile->users->count()}}
                                    @else
                                    {{ $agents->count()}}
                                    @endif
                                </td>
                                <td> {{ $profile->permissions->count()  }} </td>
                                <td>
                                    @if($profile->StatutProfile == 'web')
                                    <span class="badge badge-light-primary rounded fs-12px d-inline-flex align-items-center">Plateforme web</span>
                                    @else
                                    <span class="badge badge-light-primary rounded fs-12px d-inline-flex align-items-center">Plateforme mobile</span>
                                    @endif
                                </td>
                                @if($profile->name != 'Administrateur' && $profile->users->count() == 0)
                                <td>
                                    <a href="{{ route('profile.edit', $profile->id) }}" class="btn btn-outline-primary btn-xs btn-default">
                                        <i class="fa fa-edit"></i>
                                    </a>
                                </td>

                                <td>
                                    <form method="POST" action="{{ route('profile.destroy', $profile->id) }}">
                                        @csrf
                                        @method('DELETE')
                                        <button type="submit" class="btn btn-xs btn-outline-danger btn-default show_confirm" data-toggle="tooltip" title='Delete'><i class="fa fa-times-circle"></i></button>
                                    </form>
                                </td>
                                @else
                                <td></td>
                                <td></td>
                                @endif
                            </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
@section('js')
<script type="text/javascript">
    $('.show_confirm').click(function(event) {
        var form = $(this).closest("form");
        var name = $(this).data("name");
        event.preventDefault();
        Swal.fire({
            title: '<span style="color:#d32f2f; font-size:24px; font-weight:bold;">⚠️ Suppression définitive</span>'
            , html: `
        <div style="font-size:16px; line-height:1.6;">
            <p><strong>Êtes-vous certain de vouloir supprimer ce rôle ?</strong></p>
            <p style="color:#b71c1c;">Cette action <u>ne pourra pas être annulée</u> !</p>
        </div>
    `
            , icon: 'error'
            , showCancelButton: true
            , confirmButtonText: '<i class="fas fa-check"></i> Oui, supprimer'
            , cancelButtonText: '<i class="fas fa-times"></i> Annuler'
            , confirmButtonColor: '#e53935'
            , cancelButtonColor: '#9e9e9e'
            , background: '#fff'
            , customClass: {
                popup: 'shadow border border-danger rounded-3 px-4 py-3'
                , confirmButton: 'btn btn-danger px-4 py-2'
                , cancelButton: 'btn btn-outline-secondary px-4 py-2'
            }
            , backdrop: `
        rgba(0,0,0,0.4)
        left top
        no-repeat
    `
        }).then((result) => {
            if (result.isConfirmed) {
                form.submit();
            }
        });

    });

</script>
@endsection
