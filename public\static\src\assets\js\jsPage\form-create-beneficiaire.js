$(document).ready(function () {
    // Méthode personnalisée pour les PDF
    $.validator.addMethod("filetype", function (value, element, param) {
        let allowedTypes = param.split('|');
        if (element.files.length === 0) return true;
        let fileName = element.files[0].name.toLowerCase();
        let extension = fileName.split('.').pop();
        return allowedTypes.includes(extension);
    }, "Type de fichier invalide.");

    // Méthode pour téléphone guinéen
    $.validator.addMethod("guineaPhone", function (value, element) {
        return this.optional(element) || /^(620|621|622|623|624|625|626|627|628|629|610|611|612|613|660|661|662|663|664|665|666|654|655|656)\d{6}$/.test(value);
    }, "Veuillez entrer un numéro de téléphone guinéen valide");

    // Validation du formulaire
    $('#FormValidationBeneficiaire').validate({
        rules: {
            RaisonSociale: { required: true, minlength: 3 },
            Rccm: { required: true, minlength: 3 },
            Adresse: { required: true, minlength: 3 },
            Telephone: {
                required: true,
                digits: true,
                minlength: 9,
                maxlength: 9,
                guineaPhone: true
            },
            Email: { required: true, email: true },
            NomGerant: { required: true, minlength: 3 },
            PrenomGerant: { required: true, minlength: 3 },
            info_pdf: {
                filetype: "pdf"
            }
        },
        messages: {
            RaisonSociale: {
                required: "Ce champ est requis",
                minlength: "Veuillez saisir au minimum 3 caractères"
            },
            Rccm: {
                required: "Ce champ est requis",
                minlength: "Veuillez saisir au minimum 3 caractères"
            },
            Adresse: {
                required: "Ce champ est requis",
                minlength: "Veuillez saisir au minimum 3 caractères"
            },
            Telephone: {
                required: "Veuillez entrer un numéro de téléphone",
                digits: "Seuls les chiffres sont autorisés",
                minlength: "Le numéro doit contenir exactement 9 chiffres",
                maxlength: "Le numéro doit contenir exactement 9 chiffres",
            },
            Email: {
                required: "Ce champ est requis",
                email: "Veuillez saisir un email valide"
            },
            NomGerant: {
                required: "Ce champ est requis",
                minlength: "Veuillez saisir au minimum 3 caractères"
            },
            PrenomGerant: {
                required: "Ce champ est requis",
                minlength: "Veuillez saisir au minimum 3 caractères"
            },
            info_pdf: {
                filetype: "Seuls les fichiers PDF sont autorisés"
            }
        },
        highlight: function (element) {
            $(element).addClass('is-invalid');
        },
        unhighlight: function (element) {
            $(element).removeClass('is-invalid');
        },
        errorElement: 'span',
        errorClass: 'text-danger',
    });
});
