<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;
use <PERSON>mon\JWTAuth\Facades\JWTAuth;

class JwtAuthMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        try {
            // Verify if the JWT token is valid
           // Verify if the JWT token is valid
           $user = JWTAuth::parseToken()->authenticate();
           // If the token is valid, attach the authenticated user to the request
           $request->user = $user;

        } catch (\Exception $e) {
            // If the token is not valid, return a 401 Unauthorized response
            return response()->json(['error' => 'Unauthorized'], 401);
        }

        return $next($request);
    }
}
