<?php

namespace App\Imports;

use App\Models\Beneficiaire;
use Maatwebsite\Excel\Concerns\ToModel;
use Maatwebsite\Excel\Concerns\WithHeadingRow;
use Maatwebsite\Excel\Concerns\WithValidation;

class BeneficiaireImport implements ToModel, WithHeadingRow, WithValidation
{
    /**
     * @param array $row
     *
     * @return \Illuminate\Database\Eloquent\Model|null
     */
    public function model(array $row)
    {
        return new Beneficiaire([
            'RaisonSociale' => $row['RaisonSociale'],
            'Rccm' => $row['Rccm'],
            'forme_juridique_id' => $row['forme_juridique_id'],
            'Telephone' => $row['Telephone'],
            'Adresse' => $row['Adresse'],
            'Email' => $row['Email'],
            'NomGerant' => $row['NomGerant'],
            'PrenomGerant' => $row['PrenomGerant'],
            'Commentaire' => $row['Commentaire'],
            'user_id' => $row['user_id'],
        ]);
    }

    public function rules(): array
    {
        return [
            '*.RaisonSociale' => ['required', 'string'],
            '*.Rccm' => ['required', 'string'],
            '*.forme_juridique_id' => ['required', 'string', 'exists:forme_juridiques,id'],
            '*.Telephone' => ['required', 'string'],
            '*.Adresse' => ['required', 'string'],
            '*.Email' => ['required', 'string', 'email'],
            '*.NomGerant' => ['required', 'string'],
            '*.PrenomGerant' => ['required', 'string'],
            '*.user_id' => ['required', 'string', 'exists:users,id'],
        ];
    }

    public function customValidationMessages()
    {
        return [
            '*.RaisonSociale.required' => 'RaisonSociale obligatoire.',
            '*.Rccm.required' => 'Rccm obligatoire.',
            '*.forme_juridique_id.required' => 'FormeJuridique obligatoire.',
            '*.forme_juridique_id.exists' => 'FormeJuridique n\'existe pas.',
            '*.Telephone.required' => 'Telephone obligatoire.',
            '*.Adresse.required' => 'Adresse obligatoire.',
            '*.Email.required' => 'Email obligatoire.',
            '*.Email.email' => 'Format email non valide.',
            '*.NomGerant.required' => 'NomGérant obligatoire.',
            '*.PrenomGerant.required' => 'PrenomGerant obligatoire.',
            '*.user_id.required' => 'Id User obligatoire.',
            '*.user_id.exists' => 'Id User n\'existe pas',
        ];
    }
}
