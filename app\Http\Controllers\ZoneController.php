<?php

namespace App\Http\Controllers;

use App\Models\Porte;
use App\Models\Zone;
use Illuminate\Http\Request;
use Endroid\QrCode\Builder\Builder;
use Barryvdh\DomPDF\Facade\Pdf;
use MercurySeries\Flashy\Flashy;

class ZoneController extends Controller
{
    public function index()
    {
        $zones = Zone::all();
        return view('zone.index', compact('zones'));
    }

    public function create()
    {
        return view('zone.create');
    }

    public function store(Request $request)
    {
        $this->validate($request, [
            'CodeZone' => 'required|min:5|unique:zones',
            'NomZone' => 'required',
        ]);
        Zone::create([
            'CodeZone' => $request->CodeZone,
            'NomZone' => $request->NomZone,
        ]);

        Flashy::primary("Zone ajoutée avec succès");
        return to_route('zone.index')->onlyInput(['CodeZone', 'NomZone']);
    }

    public function show(Zone $zone)
    {
        $zone = Zone::find($zone->id);
        $portes = Porte::where('zone_id', $zone->id)->get();
        return view('portes.index', compact('zone', 'portes'));
    }

    public function edit(Zone $zone)
    {
        $zone = Zone::where('id', $zone->id)->first();
        return view('zone.edit', compact('zone'));
    }

    public function update(Request $request, Zone $zone)
    {
        $this->validate($request, [
            'CodeZone' => 'required',
            'NomZone' => 'required'
        ]);
        $zone = Zone::where('id', $zone->id)->first();
        $zone->CodeZone = $request->CodeZone;
        $zone->NomZone = $request->NomZone;
        $zone->update();
        Flashy::success("Zone modifiée avec succès");
        return redirect()->route('zone.index');
    }

    public function downloadQRCodePorte(Request $request, $porteId)
    {
        // Fetch the Porte and Zone details from the database
        $porte = Porte::findOrFail($porteId);

        // Debugging: Check if the code exists
        if (!$porte->CodePorte) {
            return response()->json(['error' => 'Code for the Porte is missing.'], 400);
        }

        $zone = $porte->zone; // Assuming a relationship exists between Porte and Zone

        $qrCode = Builder::create()->data($porte->qrCode)->size(500)->build();

        $qrCodeBase64 = base64_encode($qrCode->getString());

        // Pass data to the view
        $data = [
            'zoneName' => $zone->NomZone,
            'codePorte' => $porte->CodePorte,
            'description' => $porte->Description,
            'dateCreation' => now()->format('d/m/Y'),
            'qrCodeBase64' => $qrCodeBase64,
        ];

        // Load the view and generate the PDF
        $pdf = Pdf::loadView('qrcode_pdf', $data);

        // Return the PDF as a download
        return $pdf->download('QRCode_Porte_' . $porte->CodePorte . '.pdf');
    }

    public function destroy(Zone $zone)
    {
        $portes = Porte::where('zone_id', $zone->id)->get();
        if ($portes->count() == 0) {
            Zone::find($zone->id)->delete();
            Flashy::warning("Zone supprimée avec succès");
        } else {
            Flashy::error("Impossible, zone déjà utilisé");

        }
        return redirect()->back();
    }
}
