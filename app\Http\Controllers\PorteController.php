<?php

namespace App\Http\Controllers;

use App\Models\Porte;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Crypt;
use MercurySeries\Flashy\Flashy;

class PorteController extends Controller
{

    public function store(Request $request)
    {
        $PorteExistante = Porte::where('CodePorte', $request->input('CodePorte'))->exists();
        if ($PorteExistante) {
            // Si l'immatriculation existe déjà, afficher un message d'erreur avec Flashy
            Flashy::error("Erreur ! Code porte déjà existant, veillez le changer");
            return redirect()->back();
        }
        $this->validate($request, [
            'CodePorte' => 'required|unique:portes',
        ]);

        $porte = Porte::create([
            // 'CodePorte' => preg_replace('/\s+/', '', strtoupper($request->CodePorte)),
            'CodePorte' => strtoupper($request->CodePorte),
            'Description' => $request->Description,
            'zone_id' => $request->zone_id,
            'qrCode' => Crypt::encryptString(strtoupper($request->CodePorte)),
        ]);

        Flashy::primary("Porte ajoutée avec success");
        $nouveuZoneId = $request->zone_id;
        return redirect()->route('zone.show', $nouveuZoneId);
    }

    public function destroy(string $id, Request $request)
    {
        Porte::where('id', $id)->delete();
        Flashy::warning("Porte supprimée avec succès");
        $ZoneId = $request->zoneId;
        return redirect()->route('zone.show', $ZoneId);
    }
}
