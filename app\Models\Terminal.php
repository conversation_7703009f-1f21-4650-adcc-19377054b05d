<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Terminal extends Model
{
    use HasFactory;

    protected $fillable = [
        'NomTerminal',
        'AdresseMac',
        'NumeroSerie',
        'Description',
        'Model',
        'Commentaire',
        'marque_id',
        'statut_terminal_id',
        'agent_id',
        'porte_id',
        'user_id',
        'android_id'
    ];

    public function marque()
    {
        return $this->belongsTo(Marque::class);
    }

    public function agent()
    {
        return $this->belongsTo(Agent::class);
    }

    public function porte()
    {
        return $this->belongsTo(Porte::class);
    }
    public function statut_terminal()
    {
        return $this->belongsTo(StatutTerminal::class);
    }
}
