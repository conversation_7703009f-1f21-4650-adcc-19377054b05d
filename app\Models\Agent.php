<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Laravel\Sanctum\HasApiTokens;
use Spatie\Permission\Traits\HasRoles;

class Agent extends Model
{
    use HasFactory, HasRoles, HasApiTokens;
    protected $guard_name = "web";

    protected $fillable = [
        'CodeAgent',
        'NomAgent',
        'PrenomAgent',
        'Sexe',
        'Telephone',
        'Photo',
        'Pseudo',
        'password',
        'Statut',
        'profile_id',
        'terminal_id',
    ];

    public function terminal()
    {
        return $this->hasOne(Terminal::class);
    }

    public function scans(){
        return $this->hasMany(Scan::class);
    }
}
