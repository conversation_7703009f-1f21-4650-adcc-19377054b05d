<?php

// use App\Models\Agent;

use App\Models\Agent;
use App\Models\Carte;
use App\Models\User;
use App\Models\UserTokenManager;
use GuzzleHttp\Psr7\Response;
use Illuminate\Database\Eloquent\Casts\Json;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Response as HttpResponse;

function upload($file, $type)
{
    // composition du nom de l'image ou du fichier
    $imageName = (time() * rand(0, 1000)) . "." . $file->getClientOriginalExtension();
    // verification du type de fichier et enregistrement dans le dossier cible
    if ($type == 'image') $file->move(public_path('/uploads/images'), $imageName);
    if ($type == 'doc') $file->move(public_path('/uploads/documents'), $imageName);
    return $imageName;
}
// upload agent photos in public/uploads/images/agents/

function upload_agent_file($file, $type)
{
    // composition du nom de l'image ou du fichier
    $imageName = (time() * rand(0, 1000)) . "." . $file->getClientOriginalExtension();
    // verification du type de fichier et enregistrement dans le dossier cible
    if ($type == 'image') $file->move(public_path('uploads/images/agents'), $imageName);
    if ($type == 'doc') $file->move(public_path('uploads/documents/agents'), $imageName);
    return $imageName;
}

// generate un code client
function generateCodeSociete()
{
    // Récupère les deux derniers chiffres de l'année en cours
    $annee = date('y');
    // Récupère le mois en cours avec un zéro devant pour les mois de 1 à 9
    $mois = str_pad(date('m'), 2, '0', STR_PAD_LEFT);
    // Concatène les deux premières lettres, les deux derniers chiffres de l'année, et le mois
    $code = 'CS0' . $annee . $mois;
    // Compte le nombre de sociétés dont le role est Operateur enregistrées pour le mois et l'année actuels
    $count = User::whereYear('created_at', '=', date('Y'))
        ->whereMonth('created_at', '=', date('m'))
        ->count() + 1;

    // Ajoute le compteur avec trois chiffres
    $code .= str_pad($count, 3, '0', STR_PAD_LEFT);
    return $code;
}
// generer un code agent
function generateCodeAgent()
{

    $annee = date('y');
    $mois = str_pad(date('m'), 2, '0', STR_PAD_LEFT);
    $codeagent = 'OP' . $annee . $mois;
    $count = Agent::whereYear('created_at', '=', date('Y'))
        ->whereMonth('created_at', '=', date('m'))
        ->count() + 1;

    $codeagent .= str_pad($count, 3, '0', STR_PAD_LEFT);
    return $codeagent;
}

// generer numero autorisation sur la carte du vehicule

function generateNumeroAutorisation()
{

    // Obtenez la date actuelle
    $date = now();
    // Obtenez les parties de la date nécessaires
    $annee = substr($date->format('Y'), -2); // Les deux derniers chiffres de l'année
    $mois = $date->format('m'); // Deux chiffres pour le mois
    $jour = $date->format('d'); // Deux chiffres pour le jour
    // Obtenez le nombre total de cartes enregistrées aujourd'hui
    $nombreCartesAujourdhui = Carte::whereDate('created_at', $date->toDateString())->count();
    // Incrémentez le compteur des cartes et formatez-le avec des zéros à gauche
    $compteur = str_pad($nombreCartesAujourdhui + 1, 3, '0', STR_PAD_LEFT);
    // Concaténez les parties pour former le code complet
    $codeCarte = $annee . $mois . $jour . $compteur;
    return $codeCarte;
}

function search_numeroAutorisation_card($query)
{
    // Utiliser une requête conditionnelle
    $cartesQuery = Carte::query(); // Crée une instance de requête

    if ($query) {
        $cartesQuery->where('NumeroAutorisation', 'LIKE', "%{$query}%")->get();
    }
    return $cartesQuery;
}


//Fonction de génération de token pour l'agent
function generateAgentToken($agent)
{
    // Vérifie si un token existe déjà pour cet agent
    $existingToken = $agent->tokens()->first();

    if (!$existingToken) {
        // Création d’un nouveau token
        $newToken = $agent->createToken("TOKEN_CONNEXION_AGENT");

        // Enregistrement dans la table UserTokenManager
        UserTokenManager::create([
            'agent_id' => $agent->id,
            'token' => $newToken->accessToken->token,
            'token_value' => $newToken->plainTextToken,
        ]);

        return response()->json([
            'statut' => 200,
            'agent_id' => $agent->id,
            'token' => $newToken->plainTextToken,
        ]);
    }

    // Vérifie dans UserTokenManager si ce token existe
    $tokenManager = UserTokenManager::where('token', $existingToken->token)->first();

    if ($tokenManager) {
        return response()->json([
            'statut' => 200,
            'agent_id' => $agent->id,
            'token' => $tokenManager->token_value,
        ]);
    }

    // Si le token n’existe pas dans UserTokenManager, on peut forcer sa création
    $freshToken = $agent->createToken("TOKEN_CONNEXION_AGENT");

    UserTokenManager::create([
        'agent_id' => $agent->id,
        'token' => $freshToken->accessToken->token,
        'token_value' => $freshToken->plainTextToken,
    ]);

    return response()->json([
        'statut' => 200,
        'agent_id' => $agent->id,
        'token' => $freshToken->plainTextToken,
    ]);
}
