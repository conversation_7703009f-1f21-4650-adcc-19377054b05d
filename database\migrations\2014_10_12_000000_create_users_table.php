<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('users', function (Blueprint $table) {
            $table->id();
            $table->string('Code')->unique();
            $table->string('Nom');
            $table->string('Prenom');
            $table->string('Sexe');
            $table->string('Fonction');
            $table->string('Telephone');
            $table->string('email')->unique();
            $table->string('password');
            $table->string('Photo')->nullable();
            $table->string('Statut')->default(0);
            $table->string('parent_id')->default(0);
            $table->bigInteger('profile_id')->unsigned()->index();
            $table->foreign('profile_id')->references('id')->on('roles');
            $table->rememberToken();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('users');
    }
};
