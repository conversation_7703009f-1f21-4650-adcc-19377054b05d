<?php

namespace App\Http\Controllers;

use App\Models\Agent;
use App\Models\Carte;
use App\Models\Terminal;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;

class DashboardController extends Controller
{
    function dashboard()
    {
        // total utilisateurs web
        $total_users_web = User::count();
        // total utilisateurs compte actif
        $total_users_actifs = User::where('Statut', 1)->count();
        // total utilisateurs compte non actif
        $total_users_inactifs = User::where('Statut', 0)->count();
        // statistuqes of agents
        $total_agents = Agent::all()->count();
        $total_agents_actifs = Agent::where('Statut', 1)->count();
        $total_agents_inactifs = Agent::where('Statut', 0)->count();
        // Statistques pour les terminaux
        // liste de tous les terminaux
        $terminaux = Terminal::all()->count();
        // En stock
        $terminaux_en_stock = Terminal::whereHas('statut_terminal', function ($query) {
            $query->where('Flag', 1);
        })->count();
        // En exploitation
        $terminaux_en_exploitation = Terminal::whereHas('statut_terminal', function ($query) {
            $query->where('Flag', 2);
        })->count();
        // terminaux En réparation
        $terminaux_en_reparation = Terminal::whereHas('statut_terminal', function ($query) {
            $query->where('Flag', 3);
        })->count();
        // terminaux deteriore
        $terminaux_en_deteriore = Terminal::whereHas('statut_terminal', function ($query) {
            $query->where('Flag', 4);
        })->count();

        // totale de carte
        $total_cartes = Carte::all()->count();
        // totale de carte
        $currentYear = now()->year;
        // $total_carte_cette = Carte::whereYear('DateDelivrance', $currentYear)->count();

        $carte_actives = Carte::where('Statut', 1)->count();
        $carte_desactives = Carte::where('Statut', 0)->count();
        $carte_approuves = Carte::where('StatutApprobation', 1)->where('Statut', 1)->count();
        $carte_non_approuves = Carte::where('StatutApprobation', 0)->count();
        // Date dans 3 mois
        $troisMoisPlusTard = Carbon::now()->addMonths(3)->toDateString();

        $cartes_a_expirer_dans_3_mois = Carte::whereDate('DateExpiration', $troisMoisPlusTard)->count();
        // $cartes_a_expirer_dans_3_mois = Carte::whereBetween('DateExpiration', [Carbon::now(), Carbon::now()->addMonths(3)])->count();

        //Total des cartes non scannées
        $totalCarteNonScanne = DB::table('cartes')
            ->whereNotExists(function ($query) {
                $query->select(DB::raw(1))
                    ->from('scans')
                    ->whereColumn('scans.carte_id', 'cartes.id');
            })->count();

        return view('dashboard', compact(
            'total_users_web',
            'total_users_actifs',
            'total_users_inactifs',
            'total_agents',
            'total_agents_actifs',
            'total_agents_inactifs',
            'terminaux',
            'terminaux_en_stock',
            'terminaux_en_exploitation',
            'terminaux_en_reparation',
            'terminaux_en_deteriore',
            'cartes_a_expirer_dans_3_mois',
            'total_cartes',
            'carte_actives',
            'carte_desactives',
            'carte_approuves',
            'carte_non_approuves',
            'totalCarteNonScanne'
        ));
    }
}
