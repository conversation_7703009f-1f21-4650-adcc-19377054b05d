<?php

namespace App\Http\Controllers;

use App\Models\User;
use Illuminate\Http\Request;
use Spatie\Permission\Models\Role;
use Illuminate\Support\Facades\Auth;

use MercurySeries\Flashy\Flashy;

class UserController extends Controller
{
    public function index()
    {
        $users = User::all();
        return view('utilisateur.index', [
            'users' => $users
        ]);
    }

    // change your password or AGENT
    function update_password(Request $request, $id)
    {
        $this->validate($request, [
            'password' => 'required',
        ]);
        $user = User::where('id', $id)->first();
        $nouveauMotDePasse = $request->password;
        $user->password = bcrypt($nouveauMotDePasse);
        $user->save();
        Flashy::primary("Mot de passe réinitialisé avec succès");
        $Utilisateur = $request->user_id;
        return redirect()->route('utilisateur.show', $Utilisateur);
    }

    // profile
    function profilUser()
    {
        return view('utilisateur.profile');
    }

    // update profile user
    function updateProfile(Request $request, $id)
    {
        if ($request->hasFile('Photo')) {
            $file = $request->file('Photo');
            $user = User::where('id', $id)->first();
            $user->Nom = $request->Nom;
            $user->Prenom = $request->Prenom;
            $user->Sexe = $request->Sexe;
            $user->Fonction = $request->Fonction;
            $user->Telephone = $request->Telephone;
            $user->Photo = $request->hasFile('Photo') ? url('storage/' . $request->file('Photo')->store('uploads/images/agents', 'public')) : null;
            $user->save();
            Flashy::primary("Profile utilisateur mis à jour avec succès");
            return redirect()->back();
        }
    }

    //update password user connected
    function UpdateUserPassword(Request $request, $id)
    {
        $user = User::where('id', $id)->first();
        $MotDePasse = $request->password;
        $user->password = bcrypt($MotDePasse);
        $user->save();
        Auth::logout();
        Flashy::success("Vous avez été déconnecté parce que vous avez modifié votre mot de passe, veillez vous reconnectez");
        return redirect()->route('login');
    }

    //Create user
    public function create()
    {
        $profiles = Role::all();
        $code_user = generateCodeSociete();
        return view('utilisateur.create', [
            'profiles' => $profiles,
            'code_user' => $code_user
        ]);
    }

    //Store user in database
    public function store(Request $request)
    {
        $profile_name = Role::where('id', $request->Profile)->first();
        $user = User::create([
            'Code' => $request->Code,
            'Nom' => $request->Nom,
            'Prenom' => $request->Prenom,
            'Sexe' => $request->Sexe,
            'Fonction' => $request->Fonction,
            'Telephone' => $request->Telephone,
            'email' => $request->email,
            'password' => bcrypt($request->password),
            'Photo' =>  $request->hasFile('Photo') ? url('storage/' . $request->file('Photo')->store('uploads/images/users', 'public')) : null,
            'Statut' => 0,
            'profile_id' => $request->Profile,
            'parent_id' => auth()->user()->id
        ]);
        $user->assignRole([$profile_name->name]);
        Flashy::primary("Utilisateur ajouté avec succès");
        return redirect()->route('utilisateur.index');
    }

    // Changer le status du compte utilisateur, pour activer ou desactiver son compte
    function change_state($id)
    {
        $user = User::find($id);
        $name = $user->Prenom . ' ' . $user->Nom;
        if ($user->Statut == 1) {
            # change state to 0
            User::where('id', $id)->update(array('Statut' => 0));
            Flashy::warning("Compte de l'utilisateur $name désactivé avec succès");
        } else {
            // change state to 1
            User::where('id', $id)->update(array('Statut' => 1));
            Flashy::success("Compte de l'utilisateur $name activé avec succès");
        }
        return redirect()->route('utilisateur.index');
    }

    //  SHOW ID
    public function show(string $id)
    {
        $user = User::where('id', $id)->first();
        return view('utilisateur.show', compact('user'));
    }

    //Update user
    public function update(Request $request, string $id)
    {
        $user = User::where('id', $id)->first();
        $user->Nom =  $request->Nom;
        $user->Prenom =  $request->Prenom;
        $user->Sexe =  $request->Sexe;
        $user->Fonction =  $request->Fonction;
        $user->Telephone =  $request->Telephone;
        $user->email =  $request->email;
        $user->Photo = $request->hasFile('Photo') ? url('storage/' . $request->file('Photo')->store('uploads/images/users', 'public')) : $user->Photo;
        $user->save();
        Flashy::success("Utilisateur modifié avec succès");
        $Utilisateur = $request->user_id;
        return redirect()->route('utilisateur.show', $Utilisateur);
    }

    //Delete user
    public function destroy(string $id)
    {
        $user = User::where('id', $id)->first();
        $user->delete();
        Flashy::warning("Utilisateur supprimé avec succès");
        return redirect()->back();
    }
}
