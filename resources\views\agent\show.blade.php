@extends('layouts.base')
@section('content')
<x-breadcrumb :current="$agent->PrenomAgent . ' ' . $agent->NomAgent" previous="Agent" :previousRoute="route('agent.index')" />
<div class="row layout-spacing ">
    <!-- Content -->
    <div class="col-xl-5 col-lg-12 col-md-12 col-sm-12 layout-top-spacing">
        <div class="text-center user-profile">
            <div class="widget-content widget-content-area">
                <div class="d-flex justify-content-between">
                    <button class="edit-profile d-flex align-items-center gap-2" title="Reinitialiser le mot de passe" data-bs-toggle="modal" data-bs-target="#FormModalPasswordChange">
                        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-passport">
                            <rect width="18" height="22" x="3" y="1" rx="2" ry="2" />
                            <path d="M16 5h.01M7 5h.01M12 5h.01M7 10h10" />
                            <path d="M7 15h10" />
                        </svg>
                    </button>
                    <a href="{{ route('agent.edit', $agent->id) }}" class="mt-2 edit-profile" title="Modifier les informations personnelles">
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-edit-3">
                            <path d="M12 20h9"></path>
                            <path d="M16.5 3.5a2.121 2.121 0 0 1 3 3L7 19l-4 1 1-4L16.5 3.5z"></path>
                        </svg>
                    </a>

                    <form method="POST" action="{{ route('agent.destroy', $agent->id) }}">
                        @csrf
                        @method('DELETE')
                        <button type="submit" class="mt-2 edit-profile show_confirm" data-toggle="tooltip" title="Supprimer l'agent">
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-trash-2">
                                <polyline points="3 6 5 6 21 6"></polyline>
                                <path d="M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6m3 0V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2">
                                </path>
                                <line x1="10" y1="11" x2="10" y2="17"></line>
                                <line x1="14" y1="11" x2="14" y2="17"></line>
                            </svg>
                        </button>
                    </form>
                </div>
                <div class="text-center user-info">
                    <img src="{{ $agent->Photo ? $agent->Photo : asset('static/src/assets/img/profile-user.webp') }}" alt="Agent photo" width="30%">
                    <h3 class="mt-4">{{ $agent->PrenomAgent }} {{ $agent->NomAgent }}</h3>
                    <div class="mb-3 text-body text-opacity-50 fw-bold mt-n2">{{ $agent->email }}</div>
                    <div class="mb-1">
                        @if ($agent->Statut === 0)
                        <span class="badge text-danger  bg-opacity-15 px-2 py-6px rounded fs-12px d-inline-flex align-items-center"><i class="fa fa-circle fs-9px fa-fw me-5px"></i> &nbsp; Compte Désactivé</span>
                        @else
                        <span class="badge text-success  bg-opacity-15 px-2 py-6px rounded fs-12px d-inline-flex align-items-center"><i class="fa fa-circle fs-9px fa-fw me-5px"></i>&nbsp; Compte Activé</span>
                        @endif
                    </div>
                </div>
                <div class="user-info-list mt-2">
                    <div class="">
                        <ul class="contacts-block list-unstyled">
                            <li class="contacts-block__item">
                                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-calendar me-3">
                                    <rect x="3" y="4" width="18" height="18" rx="2" ry="2">
                                    </rect>
                                    <line x1="16" y1="2" x2="16" y2="6"></line>
                                    <line x1="8" y1="2" x2="8" y2="6"></line>
                                    <line x1="3" y1="10" x2="21" y2="10"></line>
                                </svg> Créer le: {{ \Carbon\Carbon::parse($agent->created_at)->format('d/m/Y') }}
                            </li>

                            <li class="contacts-block__item mt-2">
                                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-phone me-3">
                                    <path d="M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z">
                                    </path>
                                </svg> Télephone: {{ $agent->Telephone }}
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-7 col-lg-12 col-md-12 col-sm-12 layout-top-spacing">
        <div class="usr-tasks ">
            <div class="widget-content widget-content-area">
                <h3 class="">Statistique de scan de <b>{{ $agent->PrenomAgent }} {{ $agent->NomAgent }}</b> </h3>
                <div class="table-responsive">
                    <table id="zero-config" class="table table-bordered">
                        <thead>
                            <tr>
                                <th>Vehicule</th>
                                <th>Porte</th>
                                <th>Direction</th>
                                <th class="text-center">Date</th>
                            </tr>
                        </thead>
                        <tbody>
                            @forelse ($agent->scans as $agentScan)
                            <tr>
                                <td>{{ $agentScan->carte->vehicule->Immatriculation }}</td>
                                <td>
                                    <p>{{ $agentScan->porte->Description }}</p>
                                </td>
                                <td>
                                    @if($agentScan->Direction == "ENTREE")
                                    <p class="badge badge-pill badge-info">{{ $agentScan->Direction }}</p>
                                    @else
                                    <p class="badge badge-pill badge-warning">{{ $agentScan->Direction }}</p>
                                    @endif
                                </td>
                                <td class="text-center">
                                    <p>{{ \Carbon\Carbon::parse($agentScan->DateHeure)->format('d-m-Y') }}</p>
                                </td>
                            </tr>
                            @empty
                            <tr>
                                <td></td>
                                <td></td>
                                <td></td>
                                <td colspan="4"><span class="text-danger text-center">L'agent n'a pas éffectué de scanne pour le moment</span></td>
                            </tr>
                            @endforelse
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

    </div>

    <!-- Modal -->
    <div class="modal fade inputForm-modal" id="FormModalPasswordChange" tabindex="-1" data-bs-backdrop="static" role="dialog" aria-labelledby="inputFormModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered modal-lg" role="document">
            <div class="modal-content bg-light ">
                <div class="modal-header" id="FormModalPasswordChangeLabel">
                    <h5 class="modal-title">Modification du mot de passe de l'agent {{ $agent->PrenomAgent . ' ' . $agent->NomAgent }}</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-hidden="true">
                    </button>
                </div>
                <div class="modal-body">
                    <form id="FormValidationPassword" action="{{ route('changePasswordAgent', $agent->id)}}" class="mt-0" method="POST">
                        @csrf
                        @method('PUT')
                        <div class="mb-3 row">
                            <label for="password" class="col-sm-4 col-form-label">Nouveau mot de passe*</label>
                            <div class="col-sm-8">
                                <input type="password" class="form-control text-black" id="password" name="password">
                            </div>
                        </div>
                        <div class="mb-3 row">
                            <label for="password_confirmation" class="col-sm-4 col-form-label">Confirmation mot de passe *</label>
                            <div class="col-sm-8">
                                <input type="password" class="form-control" id="password_confirmation" name="password_confirmation">
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="reset" class="btn btn-light-danger mt-2 mb-2 btn-no-effect" data-bs-dismiss="modal">Annuler</button>
                            <button type="submit" class="btn btn-dark mt-2 mb-2 btn-no-effect">Modifier</button>
                        </div>
                    </form>
                </div>

            </div>
        </div>
    </div>
</div>
@endsection
@section('js')
<script src="{{asset('static/src/assets/js/jsPage/change-password.js') }}"></script>
<script>
    $('.show_confirm').click(function(event) {
        var form = $(this).closest("form");
        var name = $(this).data("name");
        event.preventDefault();
        Swal.fire({
            title: '<span style="color:#d32f2f; font-size:24px; font-weight:bold;">⚠️ Suppression définitive</span>'
            , html: `
        <div style="font-size:16px; line-height:1.6;">
            <p><strong>Êtes-vous certain de vouloir supprimer l\'agent ?</strong></p>
            <p style="color:#b71c1c;">Cette action <u>ne pourra pas être annulée</u> !</p>
        </div>
    `
            , icon: 'error'
            , showCancelButton: true
            , confirmButtonText: '<i class="fas fa-check"></i> Oui, supprimer'
            , cancelButtonText: '<i class="fas fa-times"></i> Annuler'
            , confirmButtonColor: '#e53935'
            , cancelButtonColor: '#9e9e9e'
            , background: '#fff'
            , customClass: {
                popup: 'shadow border border-danger rounded-3 px-4 py-3'
                , confirmButton: 'btn btn-danger px-4 py-2'
                , cancelButton: 'btn btn-outline-secondary px-4 py-2'
            }
            , backdrop: `
        rgba(0,0,0,0.4)
        left top
        no-repeat
    `
        }).then((result) => {
            if (result.isConfirmed) {
                form.submit();
            }
        });
    });

</script>
@endsection
