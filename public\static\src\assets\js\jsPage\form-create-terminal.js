//Validation des formulaire avec JQUERY
$(document).ready(function () {
    //Validation du format des adresses mac
    $.validator.addMethod("macAddress", function (value, element) {
        return this.optional(element) || /^([0-9A-Fa-f]{2}[:-]){5}([0-9A-Fa-f]{2})$/.test(value);
    }, "Veuillez entrer une adresse MAC valide (ex: 00:1A:2B:3C:4D:5E)");

    //Validation du format des numeros de serie
    $.validator.addMethod("serialNumber", function (value, element) {
        return this.optional(element) || /^[A-Za-z0-9\-]{6,20}$/.test(value);
    }, "Veuillez entrer un numéro de série valide (6 à 20 caractères alphanumériques)");

    // Initialiser la validation sur le formulaire
    $('#FormValidationTerminal').validate({
        // Spécifier les règles de validation
        rules: {
            NomTerminal: {
                required: true,
                minlength: 5
            },
            AdresseMac: {
                required: true,
                // macAddress: true
            },
            NumeroSerie: {
                required: true,
                serialNumber: true
            },
            Model: {
                required: true,
            }
        },
        // Messages d'erreur personnalisés
        messages: {
            NomTerminal: {
                required: "Ce champ est requis",
                minlength: "Veillez saisir au minimum 5 caractères"
            },
            AdresseMac: {
                required: "Ce champ est requis",
            },
            NumeroSerie: {
                required: "Ce champ est requis",
            },
            Model: {
                required: "Ce champ est requis",
            }

        },
        // Options supplémentaires
        highlight: function (element) {
            $(element).addClass('is-invalid');
        },
        unhighlight: function (element) {
            $(element).removeClass('is-invalid');
        },
        errorElement: 'span',
        errorClass: 'text-danger',
    });
})
