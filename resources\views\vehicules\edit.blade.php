@extends('layouts.base')
@section('content')
<x-breadcrumb :current="'Modification du vehicule '.$vehicule->Immatriculation" previous="Véhicules" :previousRoute="route('beneficiaire.show', $beneficiaire->id)" />
<div id="formGrid" class="mb-5 mt-5">
    <div class="card">
        <div class="card-body">
            <form id="FormValidationVehicule" action="{{ route ('vehicules.updated', [$vehicule->id, $beneficiaire->id])}}" method="POST" enctype="multipart/form-data">
                @csrf
                @method('PUT')
                <div class="mb-3 row">
                    <label for="Immatriculation" class="col-sm-2 col-form-label">Immatriculation *</label>
                    <div class="col-sm-10">
                        <input type="text" class="form-control" id="Immatriculation" name="Immatriculation" value="{{ $vehicule->Immatriculation}}">
                    </div>
                </div>

                <div class="mb-3 row">
                    <label for="Marque" class="col-sm-2 col-form-label">Marque Voiture *</label>
                    <div class="col-sm-10">
                        <select class="form-select" id="Marque" name="marque_id">
                            @foreach($marques as $marque)
                            <option value="{{ $marque->id}}">{{ $marque->NomMarque}}</option>
                            @endforeach
                        </select>
                    </div>
                </div>

                <div class="mb-3 row">
                    <label for="Model" class="col-sm-2 col-form-label">Model voiture *</label>
                    <div class="col-sm-10">
                        <input type="text" class="form-control" id="Model" name="Model" value="{{ $vehicule->Model}}">
                    </div>
                </div>

                <div class="mb-3 row">
                    <label for="TypeVehicule" class="col-sm-2 col-form-label">Type Voiture *</label>
                    <div class="col-sm-10">
                        <select class="form-select" id="TypeVehicule" name="type_vehicule_id">
                            @foreach($typeVehicules as $typeVehicule)
                            <option value="{{ $typeVehicule->id}}">{{ $typeVehicule->NomTypeVehicule}}</option>
                            @endforeach
                        </select>
                    </div>
                </div>

                <div class="mb-3 row">
                    <label for="Couleur" class="col-sm-2 col-form-label">Couleur voiture *</label>
                    <div class="col-sm-10">
                        <input type="text" class="form-control" id="Couleur" name="Couleur" value="{{ $vehicule->Couleur}}">
                    </div>
                </div>
                <div class="mb-3 row">
                    <label for="inputCommentaire" class="col-sm-2 col-form-label">Infos du vehicule (PDF)</label>
                    <div class="col-sm-10">
                        <input type="file" name="info_pdf" id="info_pdf" class="form-control">
                    </div>
                </div>
                <div class="mb-3 row">
                    <label for="inputCommentaire" class="col-sm-2 col-form-label">Description</label>
                    <div class="col-sm-10">
                        <input type="text" name="Description" id="inputCommentaire" class="form-control" value="{{ $vehicule->Description }}" />
                    </div>
                </div>

                <div class="form-group row">
                    <div class="col-sm-10 offset-sm-2 mt-3">
                        <a href="{{ URL::previous() }}" class="btn btn-danger">Annuler</a>
                        <button type="submit" class="btn btn-dark">Modifier</button>
                    </div>
                </div>
            </form>
        </div>

    </div>
</div>

@endsection
@section('js')
<script src="{{asset('static/src/assets/js/jsPage/form-create-vehicule.js') }}"></script>
@endsection
