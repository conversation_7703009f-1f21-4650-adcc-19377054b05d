<?php

namespace App\View\Components;

use Closure;
use Illuminate\Contracts\View\View;
use Illuminate\View\Component;

class Breadcrumb extends Component
{
   public $current;
    public $previous;
    public $previousRoute;

    public function __construct($current, $previous, $previousRoute)
    {
        $this->current = $current;
        $this->previous = $previous;
        $this->previousRoute = $previousRoute;
    }

    public function render()
    {
        return view('components.breadcrumb');
    }
}
