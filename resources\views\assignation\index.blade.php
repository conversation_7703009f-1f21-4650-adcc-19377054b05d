@extends('layouts.base')
@section('content')
<div class="layout-px-spacing">
    <div class="middle-content container-xxl p-0">
        <div id="tableCustomBasic" class="col-lg-12 col-12 layout-spacing">
            <div class="d-flex justify-content-between align-items-center flex-wrap mb-4 mt-5 p-3 rounded shadow-sm bg-white border">
                <h2 class="fw-bold mb-0 text-dark">Liste des assignations</h2>
                <a href="{{ route('assignation.create') }}" class="btn btn-primary d-flex align-items-center">
                    <i class="fa fa-plus me-2"></i> Assigner
                </a>
            </div>
            <div class="statbox widget box box-shadow">
                <div class="widget-content widget-content-area">
                    <table id="zero-config" class="table style-1 dt-table-hover non-hover">
                        <thead>
                            <tr>
                                <th scope="col">Code Agent</th>
                                <th class="" scope="col">Nom et Prénom de l'agent</th>
                                <th scope="col">Nom du Terminal</th>
                                <th scope="col">Numéro serie</th>
                                <th scope="col">Modèle</th>
                                <th scope="col">Statut Terminal</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($terminaux_assignes as $terminal_assigne)
                            <tr>
                                <td>{{ $terminal_assigne->agent->CodeAgent }}</td>
                                <td>{{ $terminal_assigne->agent->PrenomAgent }} {{ $terminal_assigne->agent->NomAgent }}</td>
                                <td>{{ $terminal_assigne->NomTerminal }}</td>
                                <td>{{ $terminal_assigne->NumeroSerie }}</td>
                                <td>{{ $terminal_assigne->Model }}</td>
                                <td>
                                    @if($terminal_assigne->statut_terminal->Flag == 1)
                                    <span class="badge badge-primary text-white">{{ $terminal_assigne->statut_terminal->NomStatut }}</span>
                                    @elseif($terminal_assigne->statut_terminal->Flag == 2)
                                    <span class="badge badge-secondary text-white">{{ $terminal_assigne->statut_terminal->NomStatut }}</span>
                                    @elseif($terminal_assigne->statut_terminal->Flag == 3)
                                    <span class="badge badge-danger text-white">{{ $terminal_assigne->statut_terminal->NomStatut }}</span>
                                    @elseif($terminal_assigne->statut_terminal->Flag == 4)
                                    <span class="badge badge-warning text-white">{{ $terminal_assigne->statut_terminal->NomStatut }}</span>
                                    @else
                                    <span class="badge badge-success text-white">{{ $terminal_assigne->statut_terminal->NomStatut }}</span>
                                    @endif
                                </td>
                            </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
