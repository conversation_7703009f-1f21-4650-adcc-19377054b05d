<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class ResetAndLoadDefaultDataInDatabase extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:reset-and-loading';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Script for resetting and loading default data';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        //Verifier si c'est un environnement de production avant d'exécuté la commande, si c'est le cas alors empêché l'exécution
        if (app()->environment('production')) {
            $this->error('Action interdite en production.');
            return 1;
        }

        $this->info('Réinitialisation de la base de données...');

        // Exécute migrate:fresh (supprime toutes les tables + refait toutes les migrations)
        $this->call('migrate:fresh');

        // Appeler du seeder qui va inserer les données en base de données
        $this->call('db:seed', ['--class' => 'DatabaseSeederProduction']);

        $this->info('Base de données réinitialisée avec succès !');
    }
}
