<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class StatutTerminalSeeder extends Seeder
{
    /**
     * Run the database seeds. NomStatut
     */
    public function run(): void
    {
        DB::table('statut_terminals')->insert([
            ['NomStatut' => 'En stock', 'Flag' => 1, 'user_id' => 1],
            ['NomStatut' => 'En exploitation', 'Flag' => 2, 'user_id' => 1],
            ['NomStatut' => 'En réparation', 'Flag' => 3, 'user_id' => 1],
            ['NomStatut' => 'Détérioré', 'Flag' => 4, 'user_id' => 1],
            // Ajoutez d'autres marques au besoin
        ]);
    }
}
