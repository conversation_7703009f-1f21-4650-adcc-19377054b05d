@extends('layouts.base')
@section('content')
<div class="layout-px-spacing">
    <div class="middle-content container-xxl p-0">
        <!-- BREADCRUMB -->
        <x-breadcrumb :current="'Paramètrage du compte de'.' '.$user->Prenom . ' ' . $user->Nom" previous="Utilisateurs" :previousRoute="route('utilisateur.index')" />
        <!-- /BREADCRUMB -->
        <div class="account-settings-container layout-top-spacing">
            <div class="account-content">
                <div class="row mb-3">
                    <div class="col-md-12">
                        <ul class="nav nav-pills" id="animateLine" role="tablist">
                            <li class="nav-item" role="presentation">
                                <button class="nav-link active" id="animated-underline-home-tab" data-bs-toggle="tab" href="#animated-underline-home" role="tab" aria-controls="animated-underline-home" aria-selected="true"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-home">
                                        <path d="M3 9l9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"></path>
                                        <polyline points="9 22 9 12 15 12 15 22"></polyline>
                                    </svg> Informations personnelles</button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="animated-underline-profile-tab" data-bs-toggle="tab" href="#animated-underline-profile" role="tab" aria-controls="animated-underline-profile" aria-selected="false" tabindex="-1">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-user">
                                        <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path>
                                        <circle cx="12" cy="7" r="4"></circle>
                                    </svg>
                                    Modification du mot de passe</button>
                            </li>

                        </ul>
                    </div>
                </div>

                <div class="tab-content" id="animateLineContent-4">
                    <div class="tab-pane fade show active" id="animated-underline-home" role="tabpanel" aria-labelledby="animated-underline-home-tab">
                        <div class="row">
                            <div class="col-xl-12 col-lg-12 col-md-12 layout-spacing">
                                <form id="FormValidationUser" method="POST" action="{{ route('utilisateur.update', $user->id)}}" enctype="multipart/form-data" class="section general-info">
                                    @csrf
                                    @method('PUT')
                                    <input type="hidden" value="{{ $user->id }}" name="user_id">
                                    <div class="info">
                                        <div class="row">
                                            <div class="col-lg-11 mx-auto">
                                                <div class="row">
                                                    <div class="col-xl-2 col-lg-12 col-md-4">
                                                        <div class="profile-image  mt-4 pe-md-4">
                                                            <img src="{{ $user->Photo }}" width="90%">
                                                        </div>
                                                    </div>
                                                    <div class="col-xl-10 col-lg-12 col-md-8 mt-md-0 mt-4">
                                                        <div class="form">
                                                            <div class="row">
                                                                <div class="col-md-6">
                                                                    <div class="form-group">
                                                                        <label for="Nom">Nom *</label>
                                                                        <input type="text" class="form-control mb-3" id="Nom" name="Nom" value="{{ $user->Nom}}">
                                                                    </div>
                                                                </div>

                                                                <div class="col-md-6">
                                                                    <div class="form-group">
                                                                        <label for="Prenom">Prénom *</label>
                                                                        <input type="text" class="form-control mb-3" id="Prenom" name="Prenom" value="{{ $user->Prenom}}">
                                                                    </div>
                                                                </div>

                                                                <div class="col-md-6">
                                                                    <div class="form-group">
                                                                        <label for="Sexe">Sexe *</label>
                                                                        <select class="form-select mb-3" id="Sexe" name="Sexe">
                                                                            @foreach(['H' => 'Homme', 'F' => 'Femme'] as $value => $label)
                                                                            <option value="{{ $value }}" {{ old('Sexe', $user->Sexe) == $value ? 'selected' : '' }}>
                                                                                {{ $label }}
                                                                            </option>
                                                                            @endforeach

                                                                        </select>
                                                                    </div>
                                                                </div>

                                                                <div class="col-md-6">
                                                                    <div class="form-group">
                                                                        <label for="Fonction">Fonction</label>
                                                                        <input type="text" class="form-control mb-3" id="Fonction" name="Fonction" value="{{ $user->Fonction }}">
                                                                    </div>
                                                                </div>
                                                                <div class="col-md-6">
                                                                    <div class="form-group">
                                                                        <label for="Telephone">Telephone *</label>
                                                                        <input type="text" class="form-control" id="Telephone" name="Telephone" value="{{ $user->Telephone}}" onblur="validerNumero()">
                                                                        <span class="text-danger" id="erreurNumero"></span>
                                                                    </div>
                                                                </div>
                                                                <div class="col-md-6">
                                                                    <div class="form-group">
                                                                        <label for="email">Email *</label>
                                                                        <input type="text" class="form-control mb-3" id="email" name="email" value="{{ $user->email }}">
                                                                    </div>
                                                                </div>
                                                                <div class="col-md-12">
                                                                    <div class="form-group">
                                                                        <label for="Photo">Photo</label>
                                                                        <div class="input-group mb-3">
                                                                            <input type="file" class="form-control" id="Photo" name="Photo" style="display: none;" accept="image/*">
                                                                            <button type="button" class="btn btn-outline-primary" id="btnEditPhoto">Modifier la photo</button>
                                                                        </div>

                                                                        <!-- Aperçu de l'image sélectionnée -->
                                                                        <div id="preview-container" class="mt-2">
                                                                            <img id="preview" src="{{ $user->Photo }}" alt="Aperçu" style="max-width: 200px; max-height: 200px; display: block;">
                                                                        </div>
                                                                    </div>
                                                                </div>

                                                                <div class="col-md-12 mt-1">
                                                                    <div class="form-group text-end">
                                                                        <button type="submit" class="btn btn-secondary">Modifier</button>
                                                                    </div>
                                                                </div>

                                                            </div>

                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                    <div class="tab-pane fade" id="animated-underline-profile" role="tabpanel" aria-labelledby="animated-underline-profile-tab">
                        <div class="row">
                            <div class="col-xl-6 col-lg-12 col-md-12 mx-auto layout-spacing ">
                                <div class="section general-info payment-info ">
                                    <div class="info">
                                        <h6 class="">Modification des informations de connexion</h6>
                                        <form id="FormValidationPassword" method="POST" action="{{ route('utilisateur.update_password', $user->id)}}" enctype="multipart/form-data">
                                            @csrf
                                            <input type="hidden" value="{{ $user->id }}" name="user_id">
                                            <div class="form-row mb-3">
                                                <div class="form-group col-md-12">
                                                    <div class="form-group">
                                                        <label for="password">Nouveau mot de passe</label>
                                                        <input type="password" class="form-control" id="password" name="password">

                                                    </div>
                                                </div>
                                            </div>

                                            <div class="form-row">
                                                <div class="form-group col-md-12">
                                                    <div class="form-group">
                                                        <label for="password_confirmation">Confirmation mot de passe</label>
                                                        <input type="password" class="form-control" placeholder="confirmer votre mot de passe" name="password_confirmation" id="password_confirmation" aria-describedby="passwordHelpBlock" autocomplete="off">
                                                    </div>
                                                </div>
                                            </div>

                                            <div class="col-md-12 mt-1">
                                                <div class="form-group text-end">
                                                    <button type="submit" class="btn btn-secondary">Modifier mes access</button>
                                                </div>
                                            </div>
                                        </form>
                                    </div>
                                </div>
                            </div>

                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

</div>
@endsection

@push('scripts')
<script src="{{asset('static/src/assets/js/jsPage/form-create-user.js') }}"></script>
@endpush
