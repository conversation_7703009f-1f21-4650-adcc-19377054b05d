//Validation des formulaire avec JQUERY
$(document).ready(function () {
    function updateRule(selector, condition) {
        const li = $(selector);
        const icon = li.find(".icon");

        if (condition) {
            li.removeClass('invalid').addClass('valid');
            icon.removeClass('fa-circle-xmark').addClass('fa-circle-check');
        } else {
            li.removeClass('valid').addClass('invalid');
            icon.removeClass('fa-circle-check').addClass('fa-circle-xmark');
        }
    }

    // 💡 Mise à jour dynamique des règles pendant la saisie
    $('#password').on('keyup', function () {
        const val = $(this).val();

        updateRule('#rule-length', val.length >= 8);
        updateRule('#rule-uppercase', /[A-Z]/.test(val));
        updateRule('#rule-number', /[0-9]/.test(val));
        updateRule('#rule-special', /[\W_]/.test(val));
    });

    // 🔐 Méthode personnalisée : strongPassword
    $.validator.addMethod("strongPassword", function (value, element) {
        return this.optional(element) || (
            value.length >= 8 &&
            /[A-Z]/.test(value) &&
            /[0-9]/.test(value) &&
            /[\W_]/.test(value)
        );
    }, "Validation incomplète");

    // Méthode pour vérifier le type de fichier
    $.validator.addMethod("filetype", function (value, element, param) {
        let file = element.files[0];
        if (file) {
            let fileType = file.type; // Type MIME du fichier
            return param.includes(fileType);
        }
        return false;
    });

    // Méthode pour vérifier la taille du fichier
    $.validator.addMethod("filesize", function (value, element, param) {
        let file = element.files[0];
        return file ? file.size <= param : false;
    });

    // Méthode personnalisée pour valider le préfixe du numéro de téléphone
    $.validator.addMethod("guineaPhone", function (value, element) {
        return this.optional(element) || /^(620|621|622|623|624|625|626|627|628|629|610|611|612|613|660|661|662|663|664|665|666|654|655|656)\d{6}$/.test(value);
    }, "Veuillez entrer un numéro de téléphone guinéen valide");

    $('#togglePassword').on('click', function () {
        const passwordInput = $('#password');
        const type = passwordInput.attr('type') === 'password' ? 'text' : 'password';
        passwordInput.attr('type', type);

        // Basculer l’icône (œil / œil barré)
        $(this).toggleClass('fa-eye fa-eye-slash');
    });

    // Initialiser la validation sur le formulaire
    $('#FormValidationAgent').validate({
        // Spécifier les règles de validation
        rules: {
            NomAgent: {
                required: true,
                minlength: 3
            },
            PrenomAgent: {
                required: true,
                minlength: 3
            },
            Telephone: {
                required: true,
                digits: true,
                minlength: 9,
                maxlength: 9,
                guineaPhone: true // Utilisation de la méthode personnalisée
            },
            Pseudo: {
                required: true,
            },
            Photo: {
                required: true,
                filetype: ["image/jpeg", "image/png", "image/jpg"], // Vérification stricte du type MIME
                filesize: 512000 // 500 ko (512000 octets)
            },
            password: {
                required: true,
                strongPassword: true
            },
            password_confirmation: {
                required: true,
                equalTo: "#password"
            }

        },
        // Messages d'erreur personnalisés
        messages: {
            NomAgent: {
                required: "Ce champ est requis",
                minlength: "Veillez saisir au minimum 3 caractère"
            },
            PrenomAgent: {
                required: "Ce champ est requis",
                minlength: "Veillez saisir au minimum 3 caractère"
            },
            Telephone: {
                required: "Veuillez entrer un numéro de téléphone",
                digits: "Seuls les chiffres sont autorisés",
                minlength: "Le numéro doit contenir exactement 9 chiffres",
                maxlength: "Le numéro doit contenir exactement 9 chiffres",
            },
            Pseudo: {
                required: "Ce champ est requis",
            },
            Photo: {
                required: "Veuillez sélectionner une photo",
                filetype: "Seuls les fichiers JPG, JPEG ou PNG sont autorisés",
                filesize: "La taille de l'image ne doit pas dépasser 500 Ko"
            },
            password: {
                required: "Ce champ est requis",
                strongPassword: $.validator.messages.strongPassword // message mis à jour dynamiquement
            },
            password_confirmation: {
                required: "Veuillez confirmer votre mot de passe",
                equalTo: "Les mots de passe ne correspondent pas"
            },

        },
        // Options supplémentaires
        highlight: function (element) {
            $(element).addClass('is-invalid');
        },
        unhighlight: function (element) {
            $(element).removeClass('is-invalid');
        },
        errorElement: 'span',
        errorClass: 'text-danger',
    });
});

const btnEditPhoto = document.getElementById('btnEditPhoto');
const photoInput = document.getElementById('Photo');
const preview = document.getElementById('preview');

btnEditPhoto.addEventListener('click', () => {
    photoInput.click(); // ouvre l'explorateur
});

photoInput.addEventListener('change', (event) => {
    const file = event.target.files[0];

    if (file) {
        const reader = new FileReader();
        reader.onload = function (e) {
            preview.src = e.target.result;
            preview.style.display = 'block';
        };
        reader.readAsDataURL(file);
    }
});
