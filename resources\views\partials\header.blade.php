<!-- BEGIN NAVBAR -->
<div class="header-container container-xxl">
    <header class="header navbar navbar-expand-sm expand-header d-flex align-items-center justify-content-between w-100">
        <!-- Bouton sidebar -->
        <a href="javascript:void(0);" class="sidebarCollapse">
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-menu">
                <line x1="3" y1="12" x2="21" y2="12"></line>
                <line x1="3" y1="6" x2="21" y2="6"></line>
                <line x1="3" y1="18" x2="21" y2="18"></line>
            </svg>
        </a>

        <!-- Texte défilant -->
        <div class="scrolling-text-container px-3 py-1 rounded mx-3 flex-grow-1">
            <div class="scrolling-text">
                Bienvenue chère {{ auth()->user()->Prenom . ' ' . auth()->user()->Nom }} dans VCHECK ! N'oubliez pas de vérifier les statuts des différentes cartes. 📢
            </div>
        </div>

        <!-- Menu utilisateur -->
        <ul class="navbar-item flex-row align-items-center mb-0">
            <li class="nav-item theme-toggle-item">
                <a href="javascript:void(0);" class="nav-link theme-toggle"> </a>
            </li>

            <!-- Menu Notification -->
            <li class="nav-item dropdown notification-dropdown me-3 position-relative">
                <a href="javascript:void(0);" class="nav-link dropdown-toggle position-relative" id="notificationDropdown" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                    <i class="fa-solid fa-bell fa-xl"></i>
                    <span class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger">
                        3
                    </span>
                </a>
                <div class="dropdown-menu position-absolute" aria-labelledby="notificationDropdown">
                    <div class="notification-scroll" style="max-height: 250px; overflow-y: auto;">
                        <div class="dropdown-item d-flex align-items-start">
                            <div class="me-3">
                                <i class="fa fa-info-circle text-primary"></i>
                            </div>
                            <div class="notification-content">
                                <p class="mb-0"><strong>Nouvelle mise à jour</strong></p>
                                <small>La version 2.0 est disponible</small>
                            </div>
                        </div>
                        <div class="dropdown-item d-flex align-items-start">
                            <div class="me-3">
                                <i class="fa fa-user-plus text-success"></i>
                            </div>
                            <div class="notification-content">
                                <p class="mb-0"><strong>Nouvel utilisateur</strong></p>
                                <small>Jean a rejoint votre équipe</small>
                            </div>
                        </div>
                    </div>
                    <div class="dropdown-footer text-center mt-2">
                        <a href="#">Voir toutes les notifications</a>
                    </div>
                </div>
            </li>


            <!-- Menu Profil -->
            <li class="nav-item dropdown user-profile-dropdown">
                <a href="javascript:void(0);" class="nav-link dropdown-toggle user" id="userProfileDropdown" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                    <div class="avatar-container">
                        <div class="avatar avatar-sm avatar-indicators avatar-online">
                            <img alt="User" src="{{ auth()->user()->Photo ? auth()->user()->Photo : asset('static/src/assets/img/profile-user.webp') }}" class="rounded-circle">
                        </div>
                    </div>
                </a>
                <div class="dropdown-menu position-absolute" aria-labelledby="userProfileDropdown">
                    <div class="user-profile-section">
                        <div class="media mx-auto">
                            <div class="emoji me-2">&#x1F44B;</div>
                            <div class="media-body">
                                <h5>{{ auth()->user()->Prenom }} {{ auth()->user()->Nom }}</h5>
                                <p>{{ Auth::user()->profile->name }}</p>
                            </div>
                        </div>
                    </div>
                    <div class="dropdown-item">
                        <a href="{{ route('monProfile') }}">
                            <i class="fa-solid fa-user"></i>
                            <span>Profile</span>
                        </a>
                    </div>
                    @if(auth()->user()->isAdmin())
                    <div class="dropdown-item">
                        <a href="{{ route('parametre.index') }}">
                            <i class="fa-solid fa-gear"></i>
                            <span>Paramètres</span>
                        </a>
                    </div>
                    @endif
                    <div class="dropdown-item">
                        <a href="{{ route('logout') }}">
                            <i class="fa-solid fa-right-from-bracket"></i>
                            <span>Déconnexion</span>
                        </a>
                    </div>
                </div>
            </li>

        </ul>
    </header>

    <!-- STYLE texte défilant -->
    <style>
        .scrolling-text-container {
            overflow: hidden;
            position: relative;
            white-space: nowrap;
            min-width: 0;
        }

        .scrolling-text {
            display: inline-block;
            padding-left: 100%;
            animation: scroll-left 15s linear infinite;
            font-weight: bold;
            font-size: 1rem;
        }

        @keyframes scroll-left {
            0% {
                transform: translateX(0);
            }

            100% {
                transform: translateX(-100%);
            }
        }

    </style>
</div>
<!-- END NAVBAR -->
