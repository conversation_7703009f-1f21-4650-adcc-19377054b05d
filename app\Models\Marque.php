<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Marque extends Model
{
    use HasFactory;
    protected $fillable = [
        'NomMarque',
        'user_id'
    ];

    public function vehicule(){
        return $this->hasMany(Vehicule::class);
    }
    
    public function terminals(){
        return $this->hasMany(Terminal::class);
    }
}
