<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Beneficiaire extends Model
{
    use HasFactory;
    protected $fillable = [
        'RaisonSociale',
        'forme_juridique_id',
        'Rccm',
        'Adresse',
        'Telephone',
        'Email',
        'NomGerant',
        'PrenomGerant',
        'Commentaire',
        'info_pdf',
        'user_id'
    ];
    public function forme_juridique()
    {
        return $this->belongsTo(FormeJuridique::class);
    }
    public function vehicules()
    {
        return $this->hasMany(Vehicule::class);
    }
    public function getPDFURLAttribute()
    {

        return asset('uploads/documents\\') . $this->info_pdf;
    }
    public function cartes()
    {
        return $this->hasManyThrough(Carte::class, Vehicule::class);
    }

    // funtion pour total cartes generees par beneficiaires
    function totalCartesGenerees()
    {
        return $this->cartes()->count();
    }
    // function pour total de cartes actives
    function totalCartesActives()
    {
        return $this->cartes()->where('cartes.Statut', 1)->count();
    }
    //
    // total cartes non actives
    function totalCartesNonApprouvees()
    {
        return $this->cartes()->where('cartes.StatutApprobation', 0)->count();
    }

    function user()
    {
        return $this->belongsTo(User::class);
    }
}
