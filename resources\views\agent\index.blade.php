@extends('layouts.base')
@section('content')
<div class="layout-px-spacing">
    <div class="middle-content container-xxl p-0">
        <div id="tableCustomBasic" class="col-lg-12 col-12 layout-spacing">
            <div class="d-flex justify-content-between align-items-center flex-wrap mb-4 mt-5 p-3 rounded shadow-sm bg-white border">
                <h2 class="fw-bold mb-0 text-dark">Liste des agents</h2>
                <a href="{{ route('agent.create') }}" class="btn btn-primary d-flex align-items-center">
                    <i class="fa fa-plus me-2"></i> Nouveau
                </a>
            </div>
            <div class="statbox widget box box-shadow">
                <div class="widget-content widget-content-area">
                    <table id="zero-config" class="table style-1 dt-table-hover non-hover" style="width:100%">
                        <thead>
                            <tr>
                                <th class="" scope="col">Code Agent</th>
                                <th class="">Nom et Prénoms</th>
                                <th class="">Genre</th>
                                <th class="">Télephone</th>
                                <th class="">Statut</th>
                                <th>Action</th>
                                <th>Détail</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($agents as $agent)
                            <tr>
                                <td class="">{{ $agent->CodeAgent}} </td>
                                <td class="">{{ $agent->NomAgent}} {{ $agent->PrenomAgent }} </td>
                                <td class=""> {{ $agent->Sexe }} </td>
                                <td class=""> {{ $agent->Telephone }} </td>
                                <td class="">
                                    @if($agent->Statut == 0)
                                    <span class="badge bg-danger-subtle text-danger px-3 py-2 rounded-pill">
                                        <i class="fa fa-circle me-1 fs-6"></i> Désactivé
                                    </span>
                                    @else
                                    <span class="badge bg-success-subtle text-success px-3 py-2 rounded-pill">
                                        <i class="fa fa-circle me-1 fs-6"></i> Activé
                                    </span>
                                    @endif
                                </td>
                                <td>
                                    <form action="{{ route('change_agent_state', $agent->id) }}" method="GET" id="submit{{ $agent->id}}">
                                        @if($agent->Statut === 0)
                                        <button type="button" onclick="changeAction('{{ $agent->id}}')" class="btn btn-outline-success btn-sm">Activer</button>
                                        @else
                                        <button type="button" onclick="changeAction('{{ $agent->id}}')" class="btn btn-outline-danger btn-sm">Désactiver</button>
                                        @endif
                                    </form>
                                <td class="">
                                    <div class="action-btns">
                                        <a href="{{ route('agent.show', $agent->id) }}" class="btn btn-outline-primary btn-sm" title="Voir les détails">
                                            <i class="fa fa-eye"></i>
                                        </a>
                                    </div>
                                </td>
                            </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
@section('js')
<script type="text/javascript">
    function changeAction(id) {
        let form = document.querySelector(`#submit${id}`);
        Swal.fire({
            title: 'Changement de statut'
            , html: '<strong style="color:#e2a03f">Vous êtes sur le point de change le statut du compte de l\'agent.</strong>'
            , icon: 'question'
            , showCancelButton: true
            , confirmButtonText: 'Oui, changer'
            , cancelButtonText: 'Annuler'
            , confirmButtonColor: '#f79102'
            , cancelButtonColor: '#87adbd'
            , customClass: {
                popup: 'shadow-lg rounded-lg'
                , confirmButton: 'btn btn-warning'
                , cancelButton: 'btn btn-secondary me-2'
            }
        }).then((result) => {
            if (result.isConfirmed) {
                form.submit();
            }
        });
    }

</script>
@endsection
