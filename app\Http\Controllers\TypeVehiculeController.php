<?php

namespace App\Http\Controllers;

use App\Models\TypeVehicule;
use App\Models\Vehicule;
use Illuminate\Http\Request;
use MercurySeries\Flashy\Flashy;

class TypeVehiculeController extends Controller
{
    public function index()
    {
        $TypeVehicules = TypeVehicule::all();
        $TypeVehiculesCount = TypeVehicule::whereNull('created_at')->count();
        return view('typevehicule.index', compact('TypeVehicules', 'TypeVehiculesCount'));
    }

    public function store(Request $request)
    {
        $this->validate($request, [
            'NomTypeVehicule' => 'required|min:2',
        ]);

        TypeVehicule::create([
            'NomTypeVehicule' => $request->NomTypeVehicule
        ]);
        Flashy::primary("Type voiture ajouté avec succès");
        return redirect()->route('TypeVehicule.index');
    }

    public function edit(string $id)
    {
        $typeVehicule = TypeVehicule::find($id);
        return view('typevehicule.edit', compact('typeVehicule'));
    }

    public function update(Request $request, string $id)
    {
        $this->validate($request, [
            'NomTypeVehicule' => 'required|min:2',
        ]);

        $typeVehicule = TypeVehicule::find($id);
        $typeVehicule->NomTypeVehicule = $request->NomTypeVehicule;
        $typeVehicule->save();

        Flashy::success("Type voiture modifié avec succès");
        return redirect()->route('TypeVehicule.index');
    }

    public function destroy(string $id)
    {
        $vehicules = Vehicule::where('type_vehicule_id', $id)->get();
        if ($vehicules->count() == 0) {
            TypeVehicule::find($id)->delete();
            Flashy::warning("Type voiture supprimé avec succès");
            return back();
        } else {
            Flashy::error("Impossible, type véhicule déjà utilisé");
            return back();
        }
    }
}
