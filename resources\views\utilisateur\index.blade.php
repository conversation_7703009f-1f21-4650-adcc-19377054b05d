@extends('layouts.base')
@section('content')
<div class="layout-px-spacing">
    <div class="middle-content container-xxl p-0">
        <div id="tableCustomBasic" class="col-lg-12 layout-spacing">
            <!-- Header -->
            <div class="d-flex justify-content-between align-items-center flex-wrap mb-4 mt-5 p-3 rounded shadow-sm bg-white border">
                <h2 class="fw-bold mb-0 text-dark">👥 Liste des utilisateurs</h2>
                <a href="{{ route('utilisateur.create') }}" class="btn btn-primary d-flex align-items-center">
                    <i class="fa fa-plus me-2"></i> Nouveau
                </a>
            </div>

            <!-- Table box -->
            <div class="card shadow-sm">
                <div class="card-body">
                    <div class="table-responsive">
                        <table id="zero-config" class="table table-hover table-striped align-middle mb-0">
                            <thead class="">
                                <tr>
                                    <th>Code</th>
                                    <th class="text-center">Nom & Prénom</th>
                                    <th class="text-center">Sexe</th>
                                    <th class="text-center">Fonction</th>
                                    <th class="text-center">Statut</th>
                                    <th class="text-center">État</th>
                                    <th class="text-center">Détail</th>
                                    <th class="text-center">Supprimer</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($users as $user)
                                <tr>
                                    <td>{{ $user->Code }}</td>
                                    <td class="text-center">{{ $user->Nom }} {{ $user->Prenom }}</td>
                                    <td class="text-center">{{ $user->Sexe }}</td>
                                    <td class="text-center">
                                        <span class="badge bg-success-subtle text-success px-3 py-2 rounded-pill">
                                            {{ $user->profile->name }}
                                        </span>
                                    </td>
                                    <td class="text-center">
                                        @if($user->Statut == 0)
                                        <span class="badge bg-danger-subtle text-danger px-3 py-2 rounded-pill">
                                            <i class="fa fa-circle me-1 fs-6"></i> Désactivé
                                        </span>
                                        @else
                                        <span class="badge bg-success-subtle text-success px-3 py-2 rounded-pill">
                                            <i class="fa fa-circle me-1 fs-6"></i> Activé
                                        </span>
                                        @endif
                                    </td>
                                    <td class="text-center">
                                        @if(!$user->hasRole('Administrateur'))
                                        <form action="{{ route('change_state_user', $user->id) }}" method="GET" id="submit{{ $user->id }}">
                                            @if($user->Statut == 0)
                                            <button type="button" class="btn btn-outline-success btn-sm" onclick="changeAction('{{ $user->id }}')">
                                                Activer
                                            </button>
                                            @else
                                            <button type="button" class="btn btn-outline-danger btn-sm" onclick="changeAction('{{ $user->id }}')">
                                                Désactiver
                                            </button>
                                            @endif
                                        </form>
                                        @endif
                                    </td>
                                    <td class="text-center">
                                        @if(!$user->hasRole('Administrateur'))
                                        <a href="{{ route('utilisateur.show', $user->id) }}" class="btn btn-outline-primary btn-sm" data-bs-toggle="tooltip" title="Voir">
                                            <i class="fa fa-eye"></i>
                                        </a>
                                        @endif
                                    </td>
                                    <td class="text-center">
                                        @if($user->profile->name != "Administrateur")
                                        <form method="POST" action="{{ route('utilisateur.destroy', $user->id) }}">
                                            @csrf
                                            @method('DELETE')
                                            <button type="submit" class="btn btn-xs btn-outline-danger btn-default show_confirm" title='Suppression utilisateur'><i class="fa fa-times-circle"></i></button>
                                        </form>
                                        @endif
                                    </td>
                                </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>


@endsection
@section('js')
<script type="text/javascript">
    $('.show_confirm').click(function(event) {
        var form = $(this).closest("form");
        var name = $(this).data("name");
        event.preventDefault();
        Swal.fire({
            title: '<span style="color:#d32f2f; font-size:24px; font-weight:bold;">⚠️ Suppression définitive</span>'
            , html: `
        <div style="font-size:16px; line-height:1.6;">
            <p><strong>Êtes-vous certain de vouloir supprimer ce utilisateur ?</strong></p>
            <p style="color:#b71c1c;">Cette action <u>ne pourra pas être annulée</u> !</p>
        </div>
    `
            , icon: 'error'
            , showCancelButton: true
            , confirmButtonText: '<i class="fas fa-check"></i> Oui, supprimer'
            , cancelButtonText: '<i class="fas fa-times"></i> Annuler'
            , confirmButtonColor: '#e53935'
            , cancelButtonColor: '#9e9e9e'
            , background: '#fff'
            , customClass: {
                popup: 'shadow border border-danger rounded-3 px-4 py-3'
                , confirmButton: 'btn btn-danger px-4 py-2'
                , cancelButton: 'btn btn-outline-secondary px-4 py-2'
            }
            , backdrop: `
        rgba(0,0,0,0.4)
        left top
        no-repeat
    `
        }).then((result) => {
            if (result.isConfirmed) {
                form.submit();
            }
        });
    });

    function changeAction(id) {
        let form = document.querySelector(`#submit${id}`);
        Swal.fire({
            title: 'Changement de statut '
            , html: '<strong style="color:#e2a03f">Vous êtes sur le point de change le statut du compte de l\'utilisateur.</strong>'
            , icon: 'question'
            , showCancelButton: true
            , confirmButtonText: 'Oui, changer'
            , cancelButtonText: 'Annuler'
            , confirmButtonColor: '#f79102'
            , cancelButtonColor: '#87adbd'
            , customClass: {
                popup: 'shadow-lg rounded-lg'
                , confirmButton: 'btn btn-warning'
                , cancelButton: 'btn btn-secondary me-2'
            }
        }).then((result) => {
            if (result.isConfirmed) {
                form.submit();
            }
        });
    }

</script>
@endsection
