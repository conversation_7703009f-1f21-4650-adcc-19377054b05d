@extends('layouts.base')
@section('content')
<div class="layout-px-spacing">
    <div class="middle-content container-xxl p-0">
        <div id="formGrid">
            <x-breadcrumb :current="'Modification du profile '. $profile->name" previous="Profile" :previousRoute="route('profile.index')" />
            <div class="card">
                <div class="card-body">
                    <form method="POST" action="{{ route('profile.update', $profile->id)}}" , enctype="multipart/form-data">
                        @csrf
                        @method('PUT')
                        @include('profile.form')
                    </form>
                </div>
                <div class="hljs-container rounded-bottom">
                    <pre><code class="xml" data-url="assets/data/form-elements/code-11.json"></code></pre>
                </div>
            </div>
        </div>

    </div>
</div>
@endsection
