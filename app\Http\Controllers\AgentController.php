<?php

namespace App\Http\Controllers;

use App\Models\Agent;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use MercurySeries\Flashy\Flashy;
use Spatie\Permission\Models\Role;

class AgentController extends Controller
{
    public function index()
    {
        $agents = Agent::all();
        return view('agent.index', compact('agents'));
    }

    public function change_agent_state($id)
    {
        $agent = Agent::findOrFail($id);
        $agent->update(['Statut' => !$agent->Statut]);

        $message = $agent->Statut ? "Activation du compte effectuée avec succès" : "Désactivation du compte effectuée avec succès";
        Flashy::success($message);
        return back();
    }

    public function changePasswordAgent(Request $request, $id)
    {
        $request->validate(['password' => 'required|string|min:6']);

        $agent = Agent::findOrFail($id);
        $agent->update(['password' => Hash::make($request->password)]);

        Flashy::success("Mot de passe de l'agent réinitialisé avec succès");
        return redirect()->route('agent.show', $agent->id);
    }

    public function create()
    {
        $code_agent = generateCodeAgent();
        $profiles = Role::all();
        return view('agent.create', compact('code_agent', 'profiles'));
    }

    public function store(Request $request)
    {
        $request->validate([
            'CodeAgent' => 'required|string|unique:agents,CodeAgent',
            'NomAgent' => 'required|string',
            'PrenomAgent' => 'required|string',
            'Sexe' => 'required|string',
            'Telephone' => 'required|string',
            'Pseudo' => 'required|string|unique:agents,Pseudo',
            'password' => 'required|string|min:6',
            'profile' => 'required|exists:roles,id',
        ]);

        $profile = Role::findOrFail($request->profile);
        $photoPath = $request->hasFile('Photo')
            ? url('storage/' . $request->file('Photo')->store('uploads/images/agents', 'public'))
            : null;

        $agent = Agent::create([
            'CodeAgent' => $request->CodeAgent,
            'NomAgent' => $request->NomAgent,
            'PrenomAgent' => $request->PrenomAgent,
            'Sexe' => $request->Sexe,
            'Telephone' => $request->Telephone,
            'Photo' => $photoPath,
            'Pseudo' => $request->Pseudo,
            'password' => Hash::make($request->password),
            'profile_id' => $request->profile,
        ]);

        $agent->assignRole($profile->name);
        Flashy::primary("Enregistrement de l'agent effectué avec succès");
        return redirect()->route('agent.index');
    }

    public function show(Agent $agent)
    {
        return view('agent.show', compact('agent'));
    }

    public function edit(Agent $agent)
    {
        return view('agent.edit', compact('agent'));
    }

    public function update(Request $request, Agent $agent)
    {
        $request->validate([
            'NomAgent' => 'required|string',
            'PrenomAgent' => 'required|string',
            'Sexe' => 'required|string',
            'Telephone' => 'required|string',
            'Pseudo' => 'required|string|unique:agents,Pseudo,' . $agent->id,
        ]);

        $photoPath = $request->hasFile('Photo')
            ? url('storage/' . $request->file('Photo')->store('uploads/images/agents', 'public'))
            : $agent->Photo;

        $agent->update([
            'NomAgent' => $request->NomAgent,
            'PrenomAgent' => $request->PrenomAgent,
            'Sexe' => $request->Sexe,
            'Telephone' => $request->Telephone,
            'Photo' => $photoPath,
            'Pseudo' => $request->Pseudo,
        ]);

        Flashy::success("Modification de l'agent effectuée avec succès");
        return redirect()->route('agent.index');
    }

    public function destroy(Agent $agent)
    {
        if (is_null($agent->terminal)) {
            $agent->delete();
            Flashy::warning("Suppression de l'agent effectuée avec succès");
        } else {
            Flashy::error("Impossible ! L'agent est déjà affecté à un terminal");
        }

        return redirect()->route('agent.index');
    }
}
