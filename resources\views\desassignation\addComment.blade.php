@extends('layouts.base')
@section('content')
<div class="layout-px-spacing">
    <div class="middle-content container-xxl p-0">
        <div class="row">
            <div id="flStackForm" class="col-lg-12 layout-spacing layout-top-spacing">
                <div class="statbox widget box box-shadow">
                    <div class="widget-header">
                        <div class="row">
                            <div class="col-xl-12 col-md-12 col-sm-12 col-12">
                                <h4>Vous etes sur le point de desassigner ce terminal à cet agent </h4>
                            </div>
                        </div>
                    </div>
                    <div class="widget-content widget-content-area">
                        <form class="mt-0" method="POST" action="{{ route('desassignAgent', $terminal->id)}}" enctype="multipart/form-data">
                            @csrf
                            <div class="mb-3 row">
                                <label for="inputFonction" class="col-sm-3 col-form-label">Statut Terminal</label>
                                <div class="col-sm-9">
                                    <select name="Statut" id="Statut" class="form-control">
                                        @foreach($statuts as $statut)
                                        @if($statut->Flag != 2)
                                        <option value="{{ $statut->id }}">{{ $statut->NomStatut }}</option>
                                        @endif
                                        @endforeach
                                    </select>
                                </div>
                            </div>
                            <div class="mb-3 row">
                                <label for="imputCommentaire" class="col-sm-3 col-form-label">Commentaire</label>
                                <div class="col-sm-9">
                                    <textarea name="Commentaire" placeholder="Ajouter la raison en quelques lignes" class="form-control" id="Commentaire" cols="10" rows="5"></textarea>
                                </div>
                            </div>
                            <div class="modal-footer">
                                <a href="{{ URL::previous() }}"   class=" btn btn-light-danger mt-2 mb-2 me-2 btn-no-effect">Annuler</a>
                                <button type="submit" class="btn btn-primary mt-2 mb-2 btn-no-effect">Désaffecter</button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>

        </div>
    </div>
</div>
@endsection
