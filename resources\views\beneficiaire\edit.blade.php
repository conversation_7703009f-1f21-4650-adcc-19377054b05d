@extends('layouts.base')
@section('content')
<x-breadcrumb :current="'Modification bénéficiaire'" previous="Bénéficiaires" :previousRoute="route('beneficiaire.index')" />
<div id="formGrid">
    <div class="card">
        <div class="card-body">
            <form id="FormValidationBeneficiaire" action="{{ route ('beneficiaire.update', $beneficiaire->id)}}" method="POST" enctype="multipart/form-data">
                @csrf
                @method('PUT')
                <div class="mb-3 row">
                    <label for="RaisonSociale" class="col-sm-2 col-form-label">Raison Sociale (Nom de l'entreprise) *</label>
                    <div class="col-sm-10">
                        <input type="text" class="form-control" id="RaisonSociale" name="RaisonSociale" value="{{ $beneficiaire->RaisonSociale}}">
                    </div>
                </div>

                <div class="mb-3 row">
                    <label for="legal_form" class="col-sm-2 col-form-label">Forme Juridique</label>
                    <div class="col-sm-10">
                        <select name="formeJuridique_id" id="formeJuridique_id" class="form-control">
                            @foreach($formes as $forme)
                            <option value="{{ $forme->id }}" @if($beneficiaire->forme_juridique_id === $forme->id || old('forme_juridique_id') === $forme->id) 'selected' @endif >{{ $forme->NomFormeJuridique }}</option>
                            @endforeach
                        </select>
                    </div>
                </div>

                <div class="mb-3 row">
                    <label for="inputRCCM" class="col-sm-2 col-form-label">RCCM *</label>
                    <div class="col-sm-10">
                        <input type="text" class="form-control" id="inputRCCM" name="Rccm" value="{{ $beneficiaire->Rccm  }}">
                    </div>
                </div>

                <div class="mb-3 row">
                    <label for="imputPrenom" class="col-sm-2 col-form-label">Adresse *</label>
                    <div class="col-sm-10">
                        <input type="text" class="form-control" id="imputPrenom" name="Adresse" value="{{ $beneficiaire->Adresse  }}">
                    </div>
                </div>

                <div class="mb-3 row">
                    <label for="numero" class="col-sm-2 col-form-label">Téléphone *</label>
                    <div class="col-sm-10">
                        <input type="number" id="numero" name="Telephone" value="{{ $beneficiaire->Telephone  }}" onblur="validerNumero()" class="form-control">
                        <span class="text-danger" id="erreurNumero"></span>
                    </div>
                </div>

                <div class="mb-3 row">
                    <label for="inputEmail3" class="col-sm-2 col-form-label">Adresse Email *</label>
                    <div class="col-sm-10">
                        <input type="email" class="form-control" id="inputEmail3" name="Email" value="{{ $beneficiaire->Email  }}">
                    </div>
                </div>
                <div class="mb-3 row">
                    <label for="inputSociale" class="col-sm-2 col-form-label">Nom du Gérant *</label>
                    <div class="col-sm-10">
                        <input type="text" class="form-control" id="inputSociale" name="NomGerant" value="{{ $beneficiaire->NomGerant  }}">
                    </div>
                </div>
                <div class="mb-3 row">
                    <label for="inputSociale" class="col-sm-2 col-form-label">Prénom du Gérant *</label>
                    <div class="col-sm-10">
                        <input type="text" class="form-control" id="inputSociale" name="PrenomGerant" value="{{ $beneficiaire->PrenomGerant  }}">
                    </div>
                </div>
                <!-- Commentaire -->
                <div class="mb-3 row">
                    <label for="inputSociale" class="col-sm-2 col-form-label">Commentaire</label>
                    <div class="col-sm-10">
                        <textarea name="Commentaire" class="form-control" id="Commentaire" cols="8" rows="8">{{ $beneficiaire->Commentaire  }}</textarea>
                    </div>
                </div>
                <div class="mb-3 row">
                    <label for="info_pdf" class="col-sm-2 col-form-label">Infos de l'entreprise (PDF)</label>
                    <div class="col-sm-10">
                        <input type="file" name="info_pdf" accept="pdf" id="info_pdf" class="form-control">
                    </div>
                </div>
                <div class="form-group row">
                    <div class="col-sm-10 offset-sm-2 mt-3">
                        <a href="{{ URL::previous() }}" class="btn btn-danger">Annuler</a>
                        <button type="submit" class="btn btn-dark">Modifier</button>
                    </div>
                </div>
            </form>
        </div>
        <div class="hljs-container rounded-bottom">
            <pre><code class="xml" data-url="assets/data/form-elements/code-11.json"></code></pre>
        </div>
    </div>
</div>

@endsection
@push('scripts')
<script src="{{asset('static/src/assets/js/jsPage/form-create-beneficiaire.js') }}"></script>
@endpush
