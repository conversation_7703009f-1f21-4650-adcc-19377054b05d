@extends('layouts.base')
@section('content')
<x-breadcrumb :current="'Modification zone'" previous="Zones" :previousRoute="route('zone.index')" />
<div class="layout-px-spacing">
    <div class="middle-content container-xxl p-0">
        <div class="row">
            <div id="flStackForm" class="col-lg-12 layout-spacing layout-top-spacing">
                <div class="statbox widget box box-shadow">
                    <div class="widget-content widget-content-area">
                        <form action="{{ route('zone.update', $zone->id)}}" method="POST" enctype="multipart/form-data">
                            @csrf
                            @method('PUT')
                            <div class="mb-3 row">
                                <label for="CodeZone" class="col-sm-2 col-form-label">Code de la zone </label>
                                <div class="col-sm-10">
                                    <input type="text" class="form-control text-black" id="CodeZone" name="CodeZone" value="{{ $zone->CodeZone}}" required>
                                    @error('CodeZone')
                                    <span class="text-danger">{{ $message }}</span>
                                    @enderror
                                </div>
                            </div>
                            <div class="mb-3 row">
                                <label for="NomZone" class="col-sm-2 col-form-label">Nom de la zone </label>
                                <div class="col-sm-10">
                                    <input type="text" class="form-control" id="NomZone" name="NomZone" value="{{ $zone->NomZone}}">
                                    @error('NomZone')
                                    <span class="text-danger">{{ $message }}</span>
                                    @enderror
                                </div>
                            </div>

                            <div class="form-group row">
                                <div class="col-sm-10 offset-sm-2 mt-3">
                                    <a href="{{ URL::previous() }}" class="btn btn-danger">Annuler</a>
                                    <button type="submit" class="btn btn-dark">Modifier la zone</button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>

        </div>
    </div>
</div>


@endsection
