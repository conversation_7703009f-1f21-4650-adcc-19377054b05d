.bg-success-subtle {
    background-color: #e6f4ea;
}

.bg-danger-subtle {
    background-color: #fcebea;
}

.table th {
    font-weight: 600;
    text-transform: uppercase;
    font-size: 13px;
}

.is-invalid {
    border-color: #dc3545;
}

.text-danger {
    color: #dc3545;
}

.dataTables_filter input {
    border-radius: 10px;
    padding: 5px 12px;
    border: 1px solid #ccc;
    width: 220px;
    display: inline-block;
    margin-left: 0.5rem;
}

.dataTables_length select {
    border-radius: 6px;
    padding: 5px 10px;
    border: 1px solid #ccc;
}

.dataTables_paginate .paginate_button {
    padding: 4px 12px !important;
    margin: 0 2px;
    border: 1px solid #dee2e6 !important;
    border-radius: 4px !important;
    background-color: #f8f9fa !important;
    color: #212529 !important;
}

.dataTables_paginate .paginate_button.current {
    background-color: #0d6efd !important;
    color: white !important;
    border-color: #0d6efd !important;
}

.dataTables_info {
    font-size: 0.9rem;
    color: #ffffff;
    margin-top: 0.5rem;
}

.dataTables_filter input {
    border: 2px solid #0d6efd;
    border-radius: 8px;
    padding: 8px 12px;
    font-size: 14px;
    width: 250px;
    background-color: #f9f9f9;
}

table.dataTable {
    width: 100% !important;
}

#password-rules {
    list-style: none;
    padding-left: 0;
    margin-top: 10px;
}

#password-rules li {
    margin: 6px 0;
    font-size: 14px;
    display: flex;
    align-items: center;
}

#password-rules .icon {
    margin-right: 8px;
    width: 20px;
    text-align: center;
}

.invalid {
    color: red;
}

.valid {
    color: green;
}

.password-wrapper {
    position: relative;
}

.password-wrapper .icon-eye {
    position: absolute;
    top: 50%;
    right: 10px;
    transform: translateY(-50%);
    cursor: pointer;
    color: #888;
}

.password-wrapper .icon-eye:hover {
    color: #000;
}
