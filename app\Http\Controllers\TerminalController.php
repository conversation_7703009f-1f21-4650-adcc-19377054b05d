<?php

namespace App\Http\Controllers;

use App\Models\Marque;
use App\Models\Porte;
use App\Models\StatutTerminal;
use App\Models\Terminal;
use Illuminate\Http\Request;
use MercurySeries\Flashy\Flashy;

class TerminalController extends Controller
{
    public function index()
    {
        $terminals = Terminal::latest()->paginate(5)->onEachSide(5);
        return view('terminal.index', [
            'terminals' => $terminals
        ]);
    }

    public function create()
    {
        $marques = Marque::all();
        $statutTerminal = StatutTerminal::all();
        return view('terminal.create', [
            'marques' => $marques,
            'statutTerminals' => $statutTerminal
        ]);
    }

    public function store(Request $request)
    {
        $this->validate($request, [
            'NomTerminal' => 'min:5',
            'AdresseMac' => 'required|min:5|unique:terminals',
            'NumeroSerie' => 'required|min:5|unique:terminals',
            'Model' => 'min:2',
        ]);
        Terminal::create([
            'NomTerminal' => $request->NomTerminal,
            'AdresseMac' => $request->AdresseMac,
            'NumeroSerie' => $request->NumeroSerie,
            'Description' => $request->Description,
            'Model' => $request->Model,
            'affecte' => 0,
            'marque_id' => $request->marque_id,
            'statut_terminal_id' => StatutTerminal::first()->id,
            'user_id' => auth()->user()->id,
        ]);
        Flashy::primary("Terminal ajouté avec succès");
        return redirect()->route('terminal.index');
    }

    public function edit(string $id)
    {
        $terminal = Terminal::find($id);
        if ($terminal->agent_id != 0) {
            flashy()->error("Impossible de modifié, car le terminal est déjà affecté");
            return redirect()->back();
        } else {
            $marques = Marque::all();
            $statutTerminals = StatutTerminal::all();
            $portes = Porte::all();
            return view('terminal.edit', compact('terminal', 'marques', 'statutTerminals', 'portes'));
        }
    }

    public function update(Request $request, string $id)
    {
        $terminal = Terminal::find($id);
        $terminal->NomTerminal = $request->NomTerminal;
        $terminal->AdresseMac = $request->AdresseMac;
        $terminal->NumeroSerie = $request->NumeroSerie;
        $terminal->Description = $request->Description;
        $terminal->Model = $request->Model;
        $terminal->marque_id = $request->marque_id;
        $terminal->statut_terminal_id = $request->statut_terminal_id;
        $terminal->user_id = auth()->user()->id;
        $terminal->update();
        Flashy::success("Terminal modifié avec succès");
        return redirect()->route('terminal.index');
    }

    public function destroy(string $id)
    {
        $terminal = Terminal::find($id);
        $terminal->delete();
        Flashy::warning('Terminal supprimé avec succès');
        return redirect()->route('terminal.index');
    }
}
