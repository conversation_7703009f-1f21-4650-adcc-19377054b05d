<style>
    .flashy {
        font-family: Arial, sans-serif;
        padding: 12px 25px;
        border-radius: 6px;
        font-weight: 400;
        position: fixed;
        bottom: 20px;
        right: 20px;
        font-size: 16px;
        color: #fff;
        opacity: 0;
        transform: translateY(40px);
        transition: opacity 0.6s ease, transform 0.6s ease;
        z-index: 9999;
        box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);
        display: flex;
        align-items: center;
        max-width: 350px;
    }

    /* Types de notifications */
    .flashy--success {
        background-color: #5cb85c;
    }

    .flashy--error {
        background-color: #d9534f;
    }

    .flashy--warning {
        background-color: #f0ad4e;
        color: #222;
    }

    .flashy--info {
        background-color: #5bc0de;
    }

    .flashy--primary {
        background-color: #337ab7;
    }

    /* Contenu */
    .flashy .flashy__body {
        flex: 1;
        color: inherit;
        text-decoration: none;
    }

    /* Bouton de fermeture */
    .flashy__close {
        cursor: pointer;
        font-size: 20px;
        margin-left: 15px;
        line-height: 1;
        font-weight: bold;
        color: inherit;
        transition: transform 0.3s ease;
    }

    .flashy__close:hover {
        transform: scale(1.2);
    }

    /* Responsive */
    @media (max-width: 600px) {
        .flashy {
            left: 10px;
            right: 10px;
            bottom: 15px;
            width: auto;
            max-width: none;
        }
    }

</style>

<script>
    function flashy(message, link) {
        // Crée la notif à partir du template
        var template = $($("#flashy-template").html());
        $(".flashy").remove(); // Supprimer ancienne notif si présente

        // Remplir contenu
        template.find(".flashy__body").html(message).attr("href", link || "#");

        // Ajouter au body
        template.appendTo("body");

        // Apparition avec animation
        setTimeout(() => {
            template.css({
                opacity: 1
                , transform: "translateY(0)"
            });
        }, 50);

        // Disparition automatique après 8s
        const hide = () => {
            template.css({
                opacity: 0
                , transform: "translateY(40px)"
            });
            setTimeout(() => template.remove(), 600); // attendre transition
        };
        const timeoutId = setTimeout(hide, 6000);

        // Fermeture manuelle
        template.on("click", ".flashy__close", function() {
            clearTimeout(timeoutId); // éviter double suppression
            hide();
        });
    }

</script>

@if(Session::has('flashy_notification.message'))
<script id="flashy-template" type="text/template">
    <div class="flashy flashy--{{ Session::get('flashy_notification.type') }}">
        <a href="#" class="flashy__body" target="_blank"></a>
        <span class="flashy__close">&times;</span>
    </div>
</script>

<script>
    flashy("{{ Session::get('flashy_notification.message') }}", "{{ Session::get('flashy_notification.link') }}");

</script>
@endif
