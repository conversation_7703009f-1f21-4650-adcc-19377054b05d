<?php

namespace App\Http\Controllers;

use App\Models\Agent;
use Illuminate\Http\Request;
use MercurySeries\Flashy\Flashy;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;

class ProfileController extends Controller
{
    public function index()
    {
        $profiles = Role::all();
        $agents = Agent::all();
        return view('profile.index', compact('profiles', 'agents'));
    }

    public function create()
    {
        $permissions = Permission::all();
        $profile = new Role();
        return view('profile.create', compact('profile', 'permissions'));
    }

    public function store(Request $request)
    {
        $this->validate($request, [
            'profile' => 'required|unique:roles,name'
        ]);
        $profile = Role::create([
            'name' => $request->profile,
            'StatutProfile' => $request->StatutProfile
        ]);
        foreach ($request->permissions as $p) {
            $profile->givePermissionTo(Permission::where('id', $p)->first());
        }
        Flashy::primary('Enregistrement du profile éffectué avec succès');
        return redirect()->route('profile.index');
    }

    public function edit(string $id)
    {
        $permissions = Permission::all();
        $profile = Role::find($id);
        return view('profile.edit', compact('permissions', 'profile'));
    }

    public function update(Request $request, string $id)
    {
        if ($request->permissions != null) {
            $profile = Role::find($id);
            $profile->update([
                'name' => $request->profile,
                'StatutProfile' => $request->StatutProfile
            ]);

            foreach ($profile->permissions as $p) {
                $profile->revokePermissionTo($p);
            }

            foreach ($request->permissions as $p) {
                $profile->givePermissionTo(Permission::where('id', $p)->first());
            }
            Flashy::success('Modification du profile éffectuée avec succès');
            return redirect()->route('profile.index');
        } else {
            Flashy::error("Echec, vous n'avez choisi aucune permission");
            return redirect()->back();
        }
    }

    public function destroy(string $id)
    {
        $role = Role::find($id);
        if ($role->users->count() == 0) {
            $role->delete();
            Flashy::warning('Suppression du profile éffectuée avec succès');
            return redirect()->back();
        } else {
            Flashy::error('Attention !! ce profile a deja un utilisateur');
            return redirect()->route('profile.index');
        }
    }
}
