<?php

namespace App\Http\Controllers;

use App\Models\Agent;
use App\Models\Terminal;
use Illuminate\Http\Request;

class AssignationController extends Controller
{
    public function index()
    {
        $terminaux_assignes = Terminal::whereNotNull('agent_id')->get();
        $terminaux = Terminal::all();

        return view('assignation.index', compact('terminaux', 'terminaux_assignes'));
    }

    public function create(Request $request)
    {
        $terminaux = Terminal::all();

        if ($terminaux->isEmpty()) {
            flashy()->error("Aucun terminal enregistré pour faire une assignation");
            return back();
        }

        $hasTerminalsInStock = Terminal::whereHas('statut_terminal', fn($q) => $q->where('Flag', 1))->exists();

        if (!$hasTerminalsInStock) {
            flashy()->error("Aucun terminal en stock. Veuillez ajouter un nouveau terminal.");
            return back();
        }

        if ($request->filled('search')) {
            $searchTerm = $request->search;
            $results = Agent::where('CodeAgent', 'like', "%{$searchTerm}%")->get();

            if ($results->isEmpty()) {
                return view('assignation.search', compact('terminaux'))->with('error', 'Agent introuvable, veuillez vérifier le code.');
            }

            foreach ($results as $agent) {
                if (Terminal::where('agent_id', $agent->id)->exists()) {
                    return view('assignation.search', compact('terminaux', 'results'))->with('error_alreadyTerminal', 'Cet agent est déjà affecté à un terminal.');
                }
            }

            return view('assignation.search', compact('terminaux', 'results'));
        }

        return view('assignation.search', compact('terminaux'));
    }

    public function searchAgent()
    {
        $agents = Agent::select('CodeAgent', 'NomAgent')->get();
        $terminaux = Terminal::latest()->paginate(5)->onEachSide(5);

        if ($terminaux->isEmpty()) {
            flashy()->error("Aucun terminal enregistré pour faire une assignation");
            return back();
        }

        $hasTerminalsInStock = Terminal::whereHas('statut_terminal', fn($q) => $q->where('Flag', 1))->exists();

        if (!$hasTerminalsInStock) {
            flashy()->error("Aucun terminal en stock. Veuillez ajouter un nouveau terminal.");
            return back();
        }

        if ($agents->isEmpty()) {
            flashy()->error("Aucun agent trouvé.");
            return back();
        }

        return view('assignation.search', compact('agents', 'terminaux'));
    }

    public function assign(Request $request)
    {
        $agentId = $request->input('agent_ids.0'); // array
        $terminalId = $request->input('terminal_id');

        $agent = Agent::findOrFail($agentId);
        $terminal = Terminal::findOrFail($terminalId);

        if (Terminal::where('agent_id', $agent->id)->exists()) {
            flashy()->error("Impossible !! Agent ayant déjà un terminal");
            return back();
        }

        if (Agent::where('terminal_id', $terminal->id)->exists()) {
            flashy()->error('Impossible !! Terminal déjà affecté à un agent.');
            return back();
        }

        $agent->update(['terminal_id' => $terminal->id]);
        $terminal->update([
            'agent_id' => $agent->id,
            'statut_terminal_id' => 2
        ]);

        flashy()->primary("Assignation effectuée avec succès");
        return redirect()->route('assignation.index');
    }
}
