@extends('layouts.base')
@section('content')
<div class="layout-px-spacing">
    <div class="middle-content container-xxl p-0">
        <div id="tableCustomBasic" class="col-lg-12 col-12 layout-spacing">
            <div class="d-flex justify-content-between align-items-center flex-wrap mb-4 mt-5 p-3 rounded shadow-sm bg-white border">
                <h2 class="fw-bold mb-0 text-dark">Désaffectation Terminal</h2>
            </div>
            <div class="statbox widget box box-shadow">
                <div class="widget-content widget-content-area">
                    <table id="zero-config" class="table style-1 dt-table-hover non-hover">
                        <thead>
                            <tr>
                                <th scope="col">Code Agent</th>
                                <th class="" scope="col">Identité</th>
                                <th scope="col">Nom du Terminal</th>
                                <th scope="col">Numéro serie</th>
                                <th scope="col">Modèle</th>
                                <th scope="col">Statut Terminal</th>
                                <th scope="col">Action</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($terminaux_assignes as $terminal_assigne)
                            <tr>
                                <td>{{ $terminal_assigne->agent->CodeAgent }}</td>
                                <td>{{ $terminal_assigne->agent->PrenomAgent }} {{ $terminal_assigne->agent->NomAgent }}
                                </td>
                                <td>{{ $terminal_assigne->NomTerminal }}</td>
                                <td>{{ $terminal_assigne->NumeroSerie }}</td>
                                <td>{{ $terminal_assigne->Model }}</td>
                                <td>
                                    @if($terminal_assigne->statut_terminal->Flag == 1)
                                    <span class="badge badge-primary text-white">{{
                                        $terminal_assigne->statut_terminal->NomStatut }}</span>
                                    @elseif($terminal_assigne->statut_terminal->Flag == 2)
                                    <span class="badge badge-secondary text-white">{{
                                        $terminal_assigne->statut_terminal->NomStatut }}</span>
                                    @elseif($terminal_assigne->statut_terminal->Flag == 3)
                                    <span class="badge badge-danger text-white">{{
                                        $terminal_assigne->statut_terminal->NomStatut }}</span>
                                    @elseif($terminal_assigne->statut_terminal->Flag == 4)
                                    <span class="badge badge-warning text-white">{{
                                        $terminal_assigne->statut_terminal->NomStatut }}</span>
                                    @else
                                    <span class="badge badge-success text-white">{{
                                        $terminal_assigne->statut_terminal->NomStatut }}</span>
                                    @endif

                                </td>
                                <td>
                                    <form method="POST" action="{{ route('desaffecter.terminal', $terminal_assigne->id) }}" id="submit{{ $terminal_assigne->id }}">
                                        @csrf
                                        <input type="hidden" name="agent_id" value="{{ $terminal_assigne->agent->id }}">
                                        <input type="hidden" name="terminal_id" value="{{ $terminal_assigne->id }}">
                                        <button type="submit" class="btn btn-xs btn-outline-warning show_confirm" data-toggle="tooltip" title='Désaffecter'> <i class="fa fa-reply-all"></i></button>
                                    </form>
                                </td>
                            </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

@endsection
@section('js')
<script type="text/javascript">
    $('.show_confirm').click(function(event) {
        event.preventDefault(); // empêcher la soumission immédiate

        var form = $(this).closest("form");

        Swal.fire({
            title: 'Changement de statut'
            , html: '<strong style="color:#e2a03f">Vous êtes sur le point de désaffecter l\'agent.</strong>'
            , icon: 'question'
            , showCancelButton: true
            , confirmButtonText: 'Oui, Désaffecter'
            , cancelButtonText: 'Annuler'
            , confirmButtonColor: '#f79102'
            , cancelButtonColor: '#87adbd'
            , customClass: {
                popup: 'shadow-lg rounded-lg'
                , confirmButton: 'btn btn-warning'
                , cancelButton: 'btn btn-secondary me-2'
            }
        }).then((result) => {
            if (result.isConfirmed) {
                form.submit();
            }
        });
    });

</script>

@endsection
