<div class="mb-3 row">
    <label for="profile" class="col-sm-2 col-form-label">Nom du profile</label>
    <div class="col-sm-6">
        <input type="text" class="form-control" name="profile" value="{{ old('profile')? old('profile'): $profile->name }}" id="profile">
        @error('profile') <span class="text-danger error">{{ $message }}</span>@enderror
    </div>
</div>
<div class="mb-3 row">
    <label for="StatutProfile" class="col-sm-2 col-form-label">Quelle plateforme </label>
    <div class="col-sm-6">
        <select name="StatutProfile" id="StatutProfile" class="form-control">
            <option value="web">Pour le web</option>
            <option value="mobile">Pour le mobile</option>
        </select>
    </div>
</div>

@php
$categorized = array_reduce($permissions->toArray(), function ($result, $item) {
$parts = explode('.', $item['name']);
$key = $parts[0];
$result[$key][] = $item;
return $result;

},[]);
@endphp

@foreach($categorized as $ky => $c)
<div class="row mb-2" id="permissions-web">
    @foreach($c as $key => $p)
    @if($key == 0)
    <div class="col-md-4">
        {{ ucfirst($ky)}}
    </div>
    @endif
    <div class="col-sm-10">
        <div class="form-check">
            <input class="form-check-input" type="checkbox" id="gridCheck1" value="{{ $p['id']}}" name="permissions[]" {{ $profile->hasPermissionTo($p['name']) ?'checked': " " }}>
            {{$p['label']}}
        </div>
    </div>
    @endforeach
</div>
@endforeach
<div class="form-group row">
    <div class="col-sm-10 offset-sm-2">
        <a href="{{ URL::previous() }}" class="btn btn-danger">Annuler</a>
        <button type="submit" class="btn btn-black ">Ajouter ce role</button>
    </div>
</div>
