<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Exports\BeneficiaireExport;
use App\Imports\BeneficiaireImport;
use Maatwebsite\Excel\Facades\Excel;
use MercurySeries\Flashy\Flashy;

class ExcelController extends Controller
{
    public function beneficiaireExport()
    {
        return Excel::download(new BeneficiaireExport, 'Bénéficiaire.xlsx');
    }
    public function beneficiaireImport(Request $request)
    {
        $request->validate([
            'file' => 'required|mimes:xlsx,xls,csv'
        ]);

        dd("Bienvenu");

        return redirect()->route('beneficiaire.index');
    }
}
