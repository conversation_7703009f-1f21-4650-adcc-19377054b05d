@extends('layouts.base')

@section('content')
<x-breadcrumb :current="'Nouvelle carte'" previous="Cartes" :previousRoute="route('carte.index')" />

<div class="layout-px-spacing">
    <div class="middle-content container-xxl p-0">
        <div class="row">
            <div id="flStackForm" class="col-lg-12 layout-spacing layout-top-spacing">
                <div class="statbox widget box box-shadow">
                    <div class="widget-content widget-content-area">
                        <form id="mainForm" action="{{ route('carte.store') }}" method="POST" enctype="multipart/form-data">
                            @csrf

                            {{-- Numéro Autorisation --}}
                            <div class="mb-3 row">
                                <label for="inputNom" class="col-sm-2 col-form-label">Numéro Autorisation *</label>
                                <div class="col-sm-10">
                                    <input type="text" class="form-control text-black" id="inputNom" name="NumeroAutorisation" value="{{ $NumeroAutorisation }}" readonly>
                                </div>
                            </div>

                            {{-- Bénéficiaire --}}
                            <div class="mb-3 row">
                                <label for="selectBeneficiaire" class="col-sm-2 col-form-label">Bénéficiaire *</label>
                                <div class="col-sm-10">
                                    <select class="form-select" id="selectBeneficiaire" name="beneficiaire" required onchange="updateVehicules()">
                                        <option value="">Veuillez sélectionner un bénéficiaire</option>
                                        @foreach($Beneficiaires as $beneficiaire)
                                        <option value="{{ $beneficiaire->id }}" {{ (isset($beneficiaireId) && $beneficiaireId == $beneficiaire->id) ? 'selected' : '' }}>
                                            {{ $beneficiaire->RaisonSociale }}
                                        </option>
                                        @endforeach
                                    </select>
                                </div>
                            </div>

                            {{-- Véhicules --}}
                            <div class="mb-3 row">
                                <label for="selectVehicules" class="col-sm-2 col-form-label">Immatriculation Véhicule *</label>
                                <div class="col-sm-10">
                                    <select class="form-select" id="selectVehicules" name="vehicule_id" required>
                                        @if(!isset($beneficiaireId))
                                        <option>Veuillez choisir un bénéficiaire pour que ses véhicules apparaissent</option>
                                        @elseif(count($vehicules) > 0)
                                        @foreach($vehicules as $vehicule)
                                        <option value="{{ $vehicule->id }}">{{ $vehicule->Immatriculation }} ({{ $vehicule->Description }})</option>
                                        @endforeach
                                        @else
                                        <option value="">Aucun véhicule disponible pour le bénéficiaire</option>
                                        @endif
                                    </select>
                                </div>
                            </div>

                            {{-- Portes d’accès --}}
                            <div class="row mb-3">
                                <label class="col-sm-2 col-form-label">Cochez les accès :</label>
                                @foreach($zones as $zone)
                                <div class="col-md-3">
                                    <div class="card mb-2">
                                        <div class="card-header">{{ $zone->NomZone }}</div>
                                        <div class="card-body">
                                            @foreach($zone->porte as $porte)
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" name="portes[]" value="{{ $porte->id }}" id="porte_{{ $porte->id }}">
                                                <label class="form-check-label" for="porte_{{ $porte->id }}">{{ $porte->CodePorte }}</label>
                                            </div>
                                            @endforeach
                                        </div>
                                    </div>
                                </div>
                                @endforeach
                            </div>

                            {{-- Commentaire --}}
                            <div class="mb-3 row">
                                <label for="Commentaire" class="col-sm-2 col-form-label">Commentaire</label>
                                <div class="col-sm-10">
                                    <textarea name="Commentaire" id="Commentaire" class="form-control" rows="5"></textarea>
                                </div>
                            </div>

                            {{-- Boutons --}}
                            <div class="col-sm-10 offset-sm-2 mt-3">
                                <a href="{{ route('carte.index') }}" class="btn btn-danger">Annuler</a>
                                <button type="submit" class="btn btn-dark">Générer la carte</button>
                            </div>
                        </form>

                        {{-- Formulaire caché pour actualiser la liste des véhicules --}}
                        <form id="updateVehiculesForm" action="{{ route('beneficiaire.vehicules') }}" method="POST" style="display: none;">
                            @csrf
                            <input type="hidden" name="beneficiaire" id="hiddenBeneficiaire">
                            <input type="hidden" name="NumeroAutorisation" value="{{ $NumeroAutorisation }}">
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@section('js')
<script>
    function updateVehicules() {
        const selectedBeneficiaire = document.getElementById('selectBeneficiaire').value;
        const vehiculeSelect = document.getElementById('selectVehicules');

        if (!selectedBeneficiaire) {
            vehiculeSelect.innerHTML = '<option value="">Veuillez choisir un bénéficiaire d\'abord</option>';
        } else {
            document.getElementById('hiddenBeneficiaire').value = selectedBeneficiaire;
            document.getElementById('updateVehiculesForm').submit();
        }
    }

</script>
@endsection
