<?php

namespace App\View\Components;

use Closure;
use Illuminate\Contracts\View\View;
use Illuminate\View\Component;

class PaginationComponent extends Component
{
    /**
     * Create a new component instance.
     */
    public $variablePaginated;
    public function __construct($variablePaginated)
    {
        $this->variablePaginated = $variablePaginated;
    }

    /**
     * Get the view / contents that represent the component.
     */
    public function render(): View|Closure|string
    {
        return view('components.pagination-component');
    }
}
