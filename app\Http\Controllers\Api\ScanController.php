<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Requests\ScanDecryptRequest;
use App\Http\Requests\ScanRequest;
use App\Models\Carte;
use App\Models\Porte;
use App\Models\Scan;
use App\Models\Terminal;
use App\Models\Vehicule;
use Carbon\Carbon;
use Exception;
use Illuminate\Contracts\Encryption\DecryptException;
use Illuminate\Support\Facades\Crypt;

class ScanController extends Controller
{
    //Decryptage du QR CODE de la carte du véhicule
    /**
     * @OA\Get(
     *     path="/api/scanVehicule/{qrCode}",
     *     summary="Decryptage qrcode carte",
     *     description="Permet de decrypté le qrCode de la carte",
     *     tags={"Scan"},
     *     security={{"bearerAuth":{}}},
     *      @OA\Parameter(
     *         name="Code Qr",
     *         in="path",
     *         description="CodeQr",
     *         required=true,
     *         @OA\Schema(type="string")
     *     ),
     *     @OA\RequestBody(
     *         @OA\JsonContent(
     *            required={"android_id", "agent_id", "CodePorte"},
     *            @OA\Property(property="android_id", type="string", example="2DJJSJAL"),
     *            @OA\Property(property="agent_id", type="integer", example=1),
     *            @OA\Property(property="CodePorte", type="string", example="PORTE-OUEST"),
     *         )
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Decryptage effectué avec succès",
     *         @OA\JsonContent(
     *             type="object",
     *             @OA\Property(property="Status", type="integer", example="200")
     *         )
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Utilisateur non authentifié",
     *         @OA\JsonContent(
     *             type="object",
     *             @OA\Property(property="message", type="string", example="Agent non authentifié.")
     *         )
     *     ),
     *      @OA\Response(
     *         response=500,
     *         description="Erreur du serveur",
     *         @OA\JsonContent(
     *             type="object",
     *             @OA\Property(property="statut", type="integer", example="500")
     *         )
     *     )
     * )
     */
    public function scanDecryptQrCodeCarte(ScanDecryptRequest $request, string $qrCode)
    {
        try {
            // 🔐 Déchiffrage du QR code
            $immatriculation = Crypt::decryptString($qrCode);

            // 🚗 Récupération du véhicule
            $vehicule = Vehicule::where('Immatriculation', $immatriculation)
                ->with([
                    'beneficiaire:id,RaisonSociale',
                    'marqueVoiture:id,NomMarque',
                    'type_vehicule:id,NomTypeVehicule'
                ])->first();

            if (!$vehicule) {
                return response()->json([
                    'statut' => 404,
                    'infoMessage' => "Véhicule introuvable"
                ], 404);
            }

            // 📱 Vérification du terminal
            $terminal = Terminal::where('agent_id', $request->agent_id)
                ->where('android_id', $request->android_id)
                ->first();

            if (!$terminal) {
                return response()->json([
                    'statut' => 403,
                    'infoMessage' => "Terminal non autorisé"
                ], 403);
            }

            // 🪪 Recherche de la carte associée
            $carte = Carte::where('vehicule_id', $vehicule->id)->first();
            if (!$carte) {
                return response()->json([
                    'statut' => 400,
                    'infoMessage' => "Impossible, veuillez scanner une carte valide"
                ]);
            }

            //Verifier si la date d'expiration de la carte est arrivé
            $carteExpire = Carte::where('vehicule_id', $vehicule->id)
                ->whereDate('DateExpiration', Carbon::now())
                ->first();

            if ($carteExpire != null) {
                return response()->json([
                    'statut' => 400,
                    'infoMessage' => "Accès impossible, la carte du véhicule {$vehicule->Immatriculation} a expirer, veuillez immédiatement contactez votre administrateur"
                ]);
            }

            // 🚪 Récupération de la porte
            $porte = Porte::where('CodePorte', $request->CodePorte)->first();
            if (!$porte) {
                return response()->json([
                    'statut' => 400,
                    'infoMessage' => "Code porte invalide"
                ]);
            }

            // 🔐 Vérifie si la carte donne accès à cette porte
            $porteIds = json_decode($carte->Portes, true);

            if (!$porte || !is_array($porteIds) || !in_array($porte->id, $porteIds)) {
                return response()->json([
                    'statut' => 400,
                    'infoMessage' => isset($vehicule)
                        ? "Le véhicule {$vehicule->Immatriculation} n'a pas accès à cette porte."
                        : "Accès refusé : porte non reconnue ou véhicule non autorisé."
                ]);
            }

            // ✅ Vérifie si la carte est active et approuvée
            if ($carte->Statut == 1 && $carte->StatutApprobation == 1) {
                return response()->json([
                    'statut' => 200,
                    'vehicule' => $vehicule,
                    'carte' => $carte
                ]);
            }

            return response()->json([
                'statut' => 400,
                'infoMessage' => "La carte du véhicule {$vehicule->Immatriculation} est inactive. Veuillez contacter l'administrateur."
            ]);
        } catch (\Illuminate\Contracts\Encryption\DecryptException $e) {
            return response()->json([
                'statut' => 500,
                'infoMessage' => "Échec du déchiffrement du QR code, veuillez réessayer"
            ], 500);
        } catch (\Exception $e) {
            return response()->json([
                'statut' => 500,
                'infoMessage' => "Erreur serveur, veuillez réessayer"
            ], 500);
        }
    }


    //Enregistrement de SCAN
    public function scanQrCodeStore(ScanRequest $request)
    {
        $errorMessage = "Erreur d'enregistrement, veuillez réessayer";
        try {
            $terminal = Terminal::where('agent_id', $request['agent_id'])
                ->where('android_id', $request['android_id'])
                ->first();
            $carteScanned = Carte::where('id', $request['carte_id'])->first();
            $porteScanned = Porte::where('CodePorte', $request['CodePorte'])->first();

            if ($terminal != null) {
                if ($carteScanned != null) {
                    if ($porteScanned != null) {
                        // Obtenir le dernier scan du jour pour ce véhicule
                        $lastScan = Scan::where('carte_id', $carteScanned->id)
                            ->whereDate('DateHeure', Carbon::now()->toDateString())
                            ->orderBy('DateHeure', 'desc')
                            ->first();

                        // Si on essaye de faire une entrée
                        if ($request['Direction'] === 'ENTREE') {
                            if ($lastScan && $lastScan->Direction === 'ENTREE') {
                                return response()->json([
                                    'statut' => 400,
                                    'infoMessage' => "Le vehicule {$carteScanned->vehicule->Immatriculation} est déjà rentré, veuillez enregitré sa sortie"
                                ]);
                            }
                        }

                        // Si on essaye de faire une sortie sans entrée préalable
                        if ($request['Direction'] === 'SORTIE') {
                            if (!$lastScan || $lastScan->Direction === 'SORTIE') {
                                return response()->json([
                                    'statut' => 400,
                                    'infoMessage' => "Le vehicule {$carteScanned->vehicule->Immatriculation} n'a fait aucune entrée, veuillez d'abord enregistrer son entrée"
                                ]);
                            }
                        }

                        Scan::create([
                            'porte_id' => $porteScanned->id,
                            'agent_id' => $request['agent_id'],
                            'carte_id' => $request['carte_id'],
                            'Direction' => strtoupper($request['Direction']),
                            'DateHeure' => Carbon::now()
                        ]);
                        return response()->json([
                            'statut' => 200
                        ], 200);
                    } else {
                        return response()->json([
                            'statut' => 400,
                            'infoMessage' => $errorMessage
                        ]);
                    }
                } else {
                    return response()->json([
                        'statut' => 400,
                        'infoMessage' => $errorMessage
                    ]);
                }
            } else {
                return response()->json([
                    'statut' => 400,
                    'infoMessage' => $errorMessage
                ]);
            }
        } catch (Exception $e) {
            return response()->json(['error' => $errorMessage], 500);
        }
    }

    //Decryptage du QR CODE de la porte pour l'ouverture des SCANS
    public function scanDecryptQrCodePorte(String $qrCode)
    {
        try {
            $decryptedPorte = Crypt::decryptString($qrCode); //Decryptage du QR Code de la porte crypter
            $porteDecrypted = Porte::where('CodePorte', $decryptedPorte)->with(['zone'])->first(); //Recherche de la porte dans la base de donnée
            if ($porteDecrypted != null) {
                return response()->json([
                    'statut' => 200,
                    'portes' => $porteDecrypted
                ], 200);
            }
        } catch (DecryptException $e) {
            return response()->json(['statut' => 500], 500);
        }
    }
}
