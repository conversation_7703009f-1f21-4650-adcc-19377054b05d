<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('beneficiaires', function (Blueprint $table) {
            $table->id();
            $table->string('RaisonSociale');
            $table->string('Rccm');
            $table->unsignedBigInteger('forme_juridique_id')->index();
            $table->foreign('forme_juridique_id')->references('id')->on('forme_juridiques')->onDelete('cascade')->onUpdate('cascade');
            $table->string('Telephone');
            $table->string('Adresse');
            $table->string('Email');
            $table->string('NomGerant');
            $table->string('PrenomGerant');
            $table->text('Commentaire')->nullable();
            $table->string('info_pdf')->nullable();
            $table->unsignedBigInteger('user_id')->index();
            $table->foreign('user_id')->references('id')->on('users')->onDelete('cascade')->onUpdate('cascade');
            
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('beneficiaires');
    }
};
