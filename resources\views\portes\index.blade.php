@extends('layouts.base')
@section('content')
<div class="layout-px-spacing">
    <div class="middle-content container-xxl p-0">
        <div class="d-flex align-items-center layout-top-spacing">
            <div>
                <a href="{{ route('zone.index') }}"><i class="fa fa-arrow-circle-left"></i> Zones </a>
            </div>
        </div>

        <div class="d-flex align-items-center mb-3">
            <div>
                <h1 class="page-header mb-0"><b>Nom zone : {{ $zone->NomZone }}</b> </h1>
            </div>

            <div class="ms-auto">
                <button type="button" class="btn btn-black fw-semibold fs-13px" data-bs-toggle="modal" data-bs-target="#modalLgPorte"><i class="fa fa-plus fa-fw me-1"></i>Nouvelle porte</button>

            </div>
        </div>


        <div class="row gx-4">
            <div class="col-xl-8">
                <div class="widget-content widget-content-area">
                    <table id="zero-config" class="table style-1 dt-table-hover non-hover">
                        <thead>
                            <tr>
                                <th>Code</th>
                                <th>Description</th>
                                <th>Detail</th>
                                <th>Supprimer</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach ($portes as $porte)
                            <tr>
                                <td class="align-middle">{{ $porte->CodePorte }}</td>
                                <td class="align-middle">{{ $porte->Description }}</td>
                                <td class="align-middle">
                                    <a href="{{ route('downloadQrCode', $porte->id) }}" class="action-btn btn-view bs-tooltip me-2" data-toggle="tooltip" data-placement="top" title="Téléchargez le QR Code">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-download">
                                            <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
                                            <polyline points="7 10 12 15 17 10"></polyline>
                                            <line x1="12" y1="15" x2="12" y2="3"></line>
                                        </svg> QR Code
                                    </a>
                                </td>

                                <td class="align-middle">
                                    <form method="POST" action="{{ route('porte.destroy', $porte->id) }}">
                                        @csrf
                                        <input type="hidden" name="zoneId" value="{{ $zone->id }}">
                                        <input name="_method" type="hidden" value="DELETE">
                                        <button type="submit" class="btn btn-xs btn-default show_delete" data-toggle="tooltip" title='Delete'><i class="fa fa-times-circle"></i></button>
                                    </form>
                                </td>
                            </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
            </div>
            <div class="col-xl-4">
                <div class="card mb-4">
                    <div class="card-header bg-none fw-bold d-flex align-items-center">
                        <div class="flex-1">
                            <div>Information sur la zone</div>
                        </div>

                    </div>
                    <div class="card-body">
                        <div class="d-flex">
                            <div class="flex-1 d-flex">
                                <div class="me-4"><i class="fa fa-calendar fa-lg fa-fw text-body text-opacity-25"></i>
                                </div>
                                <div>Créer le: {{ \Carbon\Carbon::parse($zone->created_at)->format('d/m/Y') }} </div>
                                <span class="badge bg-theme-subtle text-theme fw-bold fs-12px ms-auto me-2 d-flex align-items-center">

                                    {{ \Carbon\Carbon::parse($zone->created_at)->format('d/m/Y') }}
                                </span>
                            </div>
                        </div>
                        <hr class="my-3 opacity-1">
                        <div class="d-flex">
                            <div class="flex-1 d-flex">
                                <div class="me-3"><i class="fa fa-car fa-lg fa-fw text-body text-opacity-25"></i>
                                </div>
                                <div>Total Portes :</div>
                                <span class="fw-bold fs-12px ms-auto me-2 d-flex align-items-center">
                                    {{ $zone->porte->count() }}</span>
                            </div>
                        </div>
                        <hr class="my-3 opacity-1">
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>



<!-- ajouter une porte -->
<div class="modal fade" id="modalLgPorte" data-bs-backdrop="static">
    <div class="modal-dialog modal-lg">
        <div class="modal-content bg-light">
            <div class="modal-header">
                <div class="modal-title fs-5">Ajouter une porte</div>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form action="{{ route('porte.store') }}" method="POST" enctype="multipart/form-data">
                    @csrf
                    <input type="hidden" name="zone_id" value="{{ $zone->id ?? '' }}">
                    <div class="mb-3 row">
                        <label for="CodePorte" class="col-sm-4 col-form-label">Code de la Porte</label>
                        <div class="col-sm-8">
                            <input type="text" class="form-control" id="CodePorte" name="CodePorte" required>
                            @error('CodePorte')
                            <span class="text-danger error">Ce champ est requis </span>
                            @enderror
                        </div>
                    </div>

                    <div class="mb-3 row">
                        <label for="Description" class="col-sm-4 col-form-label">Description</label>
                        <div class="col-sm-8">
                            <textarea cols="5" rows="5" class="form-control" id="Description" name="Description" required></textarea>
                        </div>
                    </div>
                    <div class="form-group row">
                        <div class="col-sm-8 mt-3">
                            <button type="submit" class="btn btn-dark ">Enregistrer la porte</button>
                        </div>
                    </div>
                </form>
            </div>

        </div>
    </div>
</div>
@endsection

@section('js')
<script src="https://cdnjs.cloudflare.com/ajax/libs/sweetalert/2.1.0/sweetalert.min.js"></script>
<script type="text/javascript">
    $('.show_delete').click(function(event) {
        var form = $(this).closest("form");
        var name = $(this).data("name");
        event.preventDefault();
        Swal.fire({
            title: '<span style="color:#d32f2f; font-size:24px; font-weight:bold;">⚠️ Suppression définitive</span>'
            , html: `
        <div style="font-size:16px; line-height:1.6;">
            <p><strong>Êtes-vous certain de vouloir supprimer cette porte ?</strong></p>
            <p style="color:#b71c1c;">Cette action <u>ne pourra pas être annulée</u> !</p>
        </div>
    `
            , icon: 'error'
            , showCancelButton: true
            , confirmButtonText: '<i class="fas fa-check"></i> Oui, supprimer'
            , cancelButtonText: '<i class="fas fa-times"></i> Annuler'
            , confirmButtonColor: '#e53935'
            , cancelButtonColor: '#9e9e9e'
            , background: '#fff'
            , customClass: {
                popup: 'shadow border border-danger rounded-3 px-4 py-3'
                , confirmButton: 'btn btn-danger px-4 py-2'
                , cancelButton: 'btn btn-outline-secondary px-4 py-2'
            }
            , backdrop: `
        rgba(0,0,0,0.4)
        left top
        no-repeat
    `
        }).then((result) => {
            if (result.isConfirmed) {
                form.submit();
            }
        });
    });

</script>
@endsection
