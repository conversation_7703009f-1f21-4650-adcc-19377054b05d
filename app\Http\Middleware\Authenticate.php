<?php

namespace App\Http\Middleware;

use Illuminate\Auth\Middleware\Authenticate as Middleware;
use Illuminate\Http\Request;

class Authenticate extends Middleware
{
    /**
     * Get the path the user should be redirected to when they are not authenticated.
     */
    protected function redirectTo(Request $request): ?string
    {
        if ($request->expectsJson()) {
            // Retourne une réponse 401 pour les API stateless
            return response()->json(['message' => 'Non authentifié'], 401);
        }

        // Si ce n'est pas une requête JSON, redirige vers la page de login
        return route('login');
        //return $request->expectsJson() ? null : route('login');
    }
}
