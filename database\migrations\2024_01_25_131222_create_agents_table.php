<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('agents', function (Blueprint $table) {
            $table->id();
            $table->string('CodeAgent')->unique();
            $table->string('NomAgent');
            $table->string('PrenomAgent');
            $table->char('Sexe');
            $table->string('Telephone');
            $table->string('Photo');
            // $table->binary('Photo')->nullable(); // Stockage en BLOB
            // $table->string('photo_mime')->nullable(); // Type MIME de l’image
            $table->string('Pseudo');
            $table->string('password');
            $table->boolean('Statut')->default(0);
            $table->unsignedBigInteger('profile_id')->index();
            $table->foreign('profile_id')->references('id')->on('roles')->onDelete('cascade')->onUpdate('cascade');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('agents');
    }
};
