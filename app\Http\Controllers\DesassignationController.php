<?php

namespace App\Http\Controllers;

use App\Models\Agent;
use App\Models\StatutTerminal;
use App\Models\Terminal;
use Illuminate\Http\Request;

class DesassignationController extends Controller
{
    public function desassignationAgent()
    {
        $terminaux_assignes = Terminal::whereNotNull('agent_id')->get();
        return view('desassignation.index', compact('terminaux_assignes'));
    }

    public function desaffecterTerminal($id)
    {
        $terminal = Terminal::findOrFail($id);
        $statuts = StatutTerminal::all();

        return view('desassignation.addComment', compact('id', 'terminal', 'statuts'));
    }

    public function desassignAgent(Request $request, $id)
    {
        $request->validate([
            'Statut' => 'required|exists:statut_terminals,id',
            'Commentaire' => 'nullable|string|max:255',
        ]);

        $terminal = Terminal::findOrFail($id);
        $agent = Agent::where('terminal_id', $terminal->id)->first();
        $statutTerminal = StatutTerminal::findOrFail($request->Statut);

        if (!$agent) {
            flashy()->error("Impossible de désaffecter : aucun agent associé à ce terminal.");
            return redirect()->route('desassignation');
        }

        // Mise à jour des données
        $agent->update(['terminal_id' => null]);

        $terminal->update([
            'agent_id' => null,
            'android_id' => null,
            'Commentaire' => $request->Commentaire,
            'statut_terminal_id' => $statutTerminal->Flag,
        ]);

        flashy()->primary("Désaffectation effectuée avec succès.");
        return redirect()->route('desassignation');
    }
}
