@extends('layouts.base')
@section('content')
<x-breadcrumb :current="'Nouveau bénéficiaire'" previous="Bénéficiaires" :previousRoute="route('beneficiaire.index')" />
<div class="layout-px-spacing">
    <div class="middle-content container-xxl p-0">
        <div class="row">
            <div id="flStackForm" class="col-lg-12 layout-spacing layout-top-spacing">
                <div class="statbox widget box box-shadow">
                    <div class="widget-header">
                        <div class="row">
                            <div class="col-xl-12 col-md-12 col-sm-12 col-12">
                                <h4>Nouveau bénéficiaire</h4>
                            </div>
                        </div>
                    </div>
                    <div class="widget-content widget-content-area">
                        <form id="FormValidationBeneficiaire" action="{{ route('beneficiaire.store')}}" method="POST" enctype="multipart/form-data">
                            @csrf
                            <div class="mb-3 row">
                                <label for="RaisonSociale" class="col-sm-2 col-form-label">Raison Sociale (Nom de l'entreprise) *</label>
                                <div class="col-sm-10">
                                    <input type="text" class="form-control text-black" id="RaisonSociale" name="RaisonSociale" value="{{ old('RaisonSociale')}}">
                                </div>
                            </div>
                            <div class="mb-3 row">
                                <label for="formeJuridique_id" class="col-sm-2 col-form-label">Forme Juridique *</label>
                                <div class="col-sm-10">
                                    <select class="form-select" id="formeJuridique_id" name="formeJuridique_id">
                                        @foreach($formes as $forme)
                                        <option value="{{ $forme->id}}">{{ $forme->NomFormeJuridique}}</option>
                                        @endforeach
                                    </select>
                                </div>
                            </div>
                            <div class="mb-3 row">
                                <label for="Rccm" class="col-sm-2 col-form-label">RCCM *</label>
                                <div class="col-sm-10">
                                    <input type="text" class="form-control" id="Rccm" name="Rccm" value="{{ old('Rccm')}}">
                                </div>
                            </div>

                            <div class="mb-3 row">
                                <label for="Adresse" class="col-sm-2 col-form-label">Adresse *</label>
                                <div class="col-sm-10">
                                    <input type="text" class="form-control" id="Adresse" name="Adresse" value="{{ old('Adresse')}}">
                                </div>
                            </div>
                            <div class="mb-3 row">
                                <label for="Telephone" class="col-sm-2 col-form-label">Téléphone *</label>
                                <div class="col-sm-10">
                                    <input type="text" class="form-control" id="Telephone" name="Telephone" value="{{ old('Telephone')}}">
                                </div>
                            </div>
                            <div class="mb-3 row">
                                <label for="Email" class="col-sm-2 col-form-label">Adresse Email *</label>
                                <div class="col-sm-10">
                                    <input type="email" class="form-control" id="Email" name="Email" value="{{ old('Email')}}">
                                </div>
                            </div>
                            <div class="mb-3 row">
                                <label for="NomGerant" class="col-sm-2 col-form-label">Nom du Gérant *</label>
                                <div class="col-sm-10">
                                    <input type="text" class="form-control" id="NomGerant" name="NomGerant" value="{{ old('NomGerant')}}">
                                </div>
                            </div>
                            <div class="mb-3 row">
                                <label for="PrenomGerant" class="col-sm-2 col-form-label">Prénom du Gérant *</label>
                                <div class="col-sm-10">
                                    <input type="text" class="form-control" id="PrenomGerant" name="PrenomGerant" value="{{ old('PrenomGerant')}}">
                                </div>
                            </div>
                            <div class="mb-3 row">
                                <label for="info_pdf" class="col-sm-2 col-form-label">Infos de l'entreprise (PDF)</label>
                                <div class="col-sm-10">
                                    <input type="file" name="info_pdf" id="info_pdf" class="form-control">
                                </div>
                            </div>
                            <div class="mb-3 row">
                                <label for="Commentaire" class="col-sm-2 col-form-label">Commentaire</label>
                                <div class="col-sm-10">
                                    <textarea name="Commentaire" id="Commentaire" class="form-control" cols="8" rows="8" value="{{ old('Commentaire')}}"></textarea>
                                </div>
                            </div>

                            <div class="form-group row">
                                <div class="col-sm-10 offset-sm-2 mt-3">
                                    <a href="{{ URL::previous() }}" class="btn btn-danger">Annuler</a>
                                    <button type="submit" class="btn btn-dark">Ajouter le bénéficiaire</button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
@push('scripts')
<script src="{{asset('static/src/assets/js/jsPage/form-create-beneficiaire.js') }}"></script>
@endpush
