<?php

namespace App\Http\Requests;

use Illuminate\Contracts\Validation\Validator;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Http\Exceptions\HttpResponseException;

class ScanRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'agent_id' => 'required',
            'android_id' => 'required',
            'carte_id' => 'required',
            'CodePorte' => "required",
            'Direction' => 'required',
        ];
    }

    //Fonction qui sera retourné une fois qu'il y'a des erreurs de validation
    public function failedValidation(Validator $validator){
        throw new HttpResponseException(response()->json([
            'message' => "Erreur de validation",
            'errorListe' => $validator->errors()
        ]));
    }
}
