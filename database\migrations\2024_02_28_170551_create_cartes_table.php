<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('cartes', function (Blueprint $table) {
            $table->id();
            $table->string('NumeroAutorisation')->unique();
            $table->dateTime('DateApprobation')->nullable();
            $table->dateTime('DateExpiration')->nullable();
            $table->boolean('Statut')->default(0);
            $table->integer('StatutApprobation')->default(0);
            $table->text('Commentaire')->nullable();
            $table->string('Portes')->nullable();
            $table->string('CodeQR');
            $table->unsignedBigInteger('vehicule_id')->index();
            $table->foreign('vehicule_id')->references('id')->on('vehicules')->onDelete('cascade')->onUpdate('cascade');
            $table->unsignedBigInteger('user_id')->index();
            $table->foreign('user_id')->references('id')->on('users')->onDelete('cascade')->onUpdate('cascade');
            // Commentaire_desactivation
            $table->text('Commentaire_desactivation')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('cartes');
    }
};
