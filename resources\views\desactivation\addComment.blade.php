@extends('layouts.base')
@section('content')
<div class="layout-px-spacing">
    <div class="middle-content container-xxl p-0">
        <div class="row">
            <div id="flStackForm" class="col-lg-12 layout-spacing layout-top-spacing">
                <div class="statbox widget box box-shadow">
                    <div class="widget-header">
                        <div class="row">
                            <div class="col-xl-12 col-md-12 col-sm-12 col-12">
                                <h4>Vous etes sur le point de désactiver cette carte</h4>
                            </div>
                        </div>
                    </div>
                    <div class="widget-content widget-content-area">
                        <form class="mt-0" method="POST" action="{{ route('desactivate_card', $carte->id)}}" enctype="multipart/form-data">
                            @csrf 
                            <div class="mb-3 row">
                                <label for="imputCommentaire" class="col-sm-3 col-form-label">Commentaire *</label>
                                <div class="col-sm-9">
                                    <textarea name="Commentaire_desactivation" placeholder="Ajouter la raison pour desactiver cette carte en quelques lignes" class="form-control" id="Commentaire" cols="10" rows="5"></textarea>
                                </div>
                            </div>
                            <div class="modal-footer">
                                <button type="reset" class="btn btn-light-danger mt-2 mb-2 btn-no-effect" >Annuler</button>
                                <button type="submit" class="btn btn-primary mt-2 mb-2 btn-no-effect">Désactiver</button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>

        </div>
    </div>
</div>


@endsection