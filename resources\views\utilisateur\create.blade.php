@extends('layouts.base')

@section('content')
<x-breadcrumb :current="'Nouveau Utilisateur'" previous="Utilisateurs" :previousRoute="route('utilisateur.index')" />

<div class="layout-px-spacing">
    <div class="middle-content container-xxl p-0">
        <div class="row">
            <div id="flStackForm" class="col-lg-12 layout-spacing layout-top-spacing">
                <div class="statbox widget box box-shadow">
                    <div class="widget-header">
                        <div class="row">
                            <div class="col-12">
                                <h4>Ajouter un compte utilisateur</h4>
                            </div>
                        </div>
                    </div>

                    <div class="widget-content widget-content-area">
                        <form id="FormValidationUser" action="{{ route('utilisateur.store') }}" method="POST" enctype="multipart/form-data">
                            @csrf

                            {{-- Code --}}
                            @include('components.input-row', ['label' => 'Code *', 'name' => 'Code', 'value' => $code_user, 'readonly' => true])

                            {{-- Nom --}}
                            @include('components.input-row', ['label' => 'Nom *', 'name' => 'Nom', 'value' => old('Nom')])

                            {{-- Prénom --}}
                            @include('components.input-row', ['label' => 'Prénom *', 'name' => 'Prenom', 'value' => old('Prenom')])

                            {{-- Sexe --}}
                            <div class="mb-3 row">
                                <label for="Sexe" class="col-sm-2 col-form-label">Sexe *</label>
                                <div class="col-sm-10">
                                    <select name="Sexe" id="Sexe" class="form-control">
                                        <option value="M">Masculin</option>
                                        <option value="F">Féminin</option>
                                    </select>
                                </div>
                            </div>

                            {{-- Fonction --}}
                            @include('components.input-row', ['label' => 'Fonction', 'name' => 'Fonction', 'value' => old('Fonction')])

                            {{-- Téléphone --}}
                            @include('components.input-row', ['label' => 'Téléphone *', 'name' => 'Telephone', 'value' => old('Telephone')])

                            {{-- Email --}}
                            @include('components.input-row', ['label' => 'Adresse Email *', 'name' => 'email', 'type' => 'email', 'value' => old('Email')])

                            {{-- Photo --}}
                            @include('components.input-row', ['label' => 'Photo *', 'name' => 'Photo', 'type' => 'file', 'accept' => '.jpg,.jpeg,.png'])

                            {{-- Profile --}}
                            <div class="mb-3 row">
                                <label for="Profile" class="col-sm-2 col-form-label">Profile *</label>
                                <div class="col-sm-10">
                                    <select class="form-select" id="Profile" name="Profile">
                                        @foreach($profiles->where('StatutProfile', 'web') as $profile)
                                        <option value="{{ $profile->id }}">{{ $profile->name }}</option>
                                        @endforeach
                                    </select>
                                </div>
                            </div>

                            <div class="mb-4 row">
                                <label for="password" class="col-sm-2 col-form-label">
                                    Mot de passe *
                                </label>

                                <div class="col-sm-10">
                                    <div class="password-wrapper" style="position: relative;">
                                        <input type="password" class="form-control" id="password" name="password" style="padding-right: 40px;">
                                        <i id="togglePassword" class="fa-solid fa-eye icon-eye" style="position: absolute; top: 50%; right: 10px; transform: translateY(-50%); cursor: pointer;"></i>
                                    </div>

                                    <ul id="password-rules" class="mt-2">
                                        <li id="rule-length" class="invalid">
                                            <i class="fa-solid fa-circle-xmark icon"></i> Au moins 8 caractères
                                        </li>
                                        <li id="rule-uppercase" class="invalid">
                                            <i class="fa-solid fa-circle-xmark icon"></i> Au moins une lettre majuscule
                                        </li>
                                        <li id="rule-number" class="invalid">
                                            <i class="fa-solid fa-circle-xmark icon"></i> Au moins un chiffre
                                        </li>
                                        <li id="rule-special" class="invalid">
                                            <i class="fa-solid fa-circle-xmark icon"></i> Au moins un caractère spécial (!@#...)
                                        </li>
                                    </ul>
                                </div>
                            </div>

                            {{-- Confirmation mot de passe --}}
                            <div class="mb-3 row">
                                <label for="password_confirmation" class="col-sm-2 col-form-label">Confirmation *</label>
                                <div class="col-sm-10">
                                    <input type="password" class="form-control" oncopy="return false;" onpaste="return false;" oncut="return false;" name="password_confirmation" id="password_confirmation">
                                </div>
                            </div>

                            {{-- Boutons --}}
                            <div class="form-group row">
                                <div class="col-sm-10 offset-sm-2 mt-3">
                                    <a href="{{ URL::previous() }}" class="btn btn-danger">Annuler</a>
                                    <button type="submit" class="btn btn-dark">Ajouter l'utilisateur</button>
                                </div>
                            </div>
                        </form>
                    </div> <!-- widget-content -->
                </div> <!-- box -->
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script src="{{ asset('static/src/assets/js/jsPage/form-create-user.js') }}"></script>
@endpush
