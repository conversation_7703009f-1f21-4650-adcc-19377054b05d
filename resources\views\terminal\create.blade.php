@extends('layouts.base')
@section('content')
<x-breadcrumb :current="'Nouveau terminal'" previous="Terminals" :previousRoute="route('terminal.index')" />
<div class="layout-px-spacing">
    <div class="middle-content container-xxl p-0">
        <div class="row">
            <div id="flStackForm" class="col-lg-12 layout-spacing layout-top-spacing">
                <div class="statbox widget box box-shadow">
                    <div class="widget-header">
                        <div class="row">
                            <div class="col-xl-12 col-md-12 col-sm-12 col-12">
                                <h4>Ajouter un terminal</h4>
                            </div>
                        </div>
                    </div>
                    <div class="widget-content widget-content-area">
                        <form id="FormValidationTerminal" action="{{ route('terminal.store') }}" method="POST" enctype="multipart/form-data">
                            @csrf
                            <div class="mb-3 row">
                                <label for="NomTerminal" class="col-sm-2 col-form-label">Nom du terminal *</label>
                                <div class="col-sm-10">
                                    <input type="text" class="form-control text-black" id="NomTerminal" name="NomTerminal" value="{{ old('NomTerminal') }}">

                                </div>
                            </div>

                            <div class="mb-3 row">
                                <label for="AdresseMac" class="col-sm-2 col-form-label">Adresse Mac *</label>
                                <div class="col-sm-10">
                                    <input type="text" class="form-control" id="AdresseMac" name="AdresseMac" value="{{ old('AdresseMac') }}">

                                </div>
                            </div>

                            <div class="mb-3 row">
                                <label for="NumeroSerie" class="col-sm-2 col-form-label">Numero de serie *</label>
                                <div class="col-sm-10">
                                    <input type="text" class="form-control" id="NumeroSerie" name="NumeroSerie" value="{{ old('NumeroSerie') }}">

                                </div>
                            </div>

                            <div class="mb-3 row">
                                <label for="marque_id" class="col-sm-2 col-form-label">Marque Terminal *</label>
                                <div class="col-sm-10">
                                    <select class="form-select" id="marque_id" name="marque_id">
                                        @foreach($marques as $marque)
                                        <option value="{{ $marque->id}}">{{ $marque->NomMarque}}</option>
                                        @endforeach
                                    </select>
                                </div>
                            </div>
                            <div class="mb-3 row">
                                <label for="Model" class="col-sm-2 col-form-label">Model Terminal *</label>
                                <div class="col-sm-10">
                                    <input type="text" class="form-control" id="Model" name="Model" value="{{ old('Model') }}">
                                </div>
                            </div>
                            <div class="mb-3 row">
                                <label for="Description" class="col-sm-2 col-form-label">Description </label>
                                <div class="col-sm-10">
                                    <textarea name="Description" placeholder="Ajouter une description du terminal" class="form-control" id="Description" cols="10" rows="5"></textarea>
                                </div>
                            </div>
                            <div class="form-group row">
                                <div class="col-sm-10 offset-sm-2 mt-3">
                                    <a href="{{ route('terminal.index') }}" class="btn btn-danger">Annuler</a>
                                    <button type="submit" class="btn btn-dark">Ajouter le terminal</button>
                                </div>
                        </form>
                    </div>
                </div>
            </div>

        </div>
    </div>
</div>
@endsection
@push('scripts')
<script src="{{asset('static/src/assets/js/jsPage/form-create-terminal.js') }}"></script>
@endpush
