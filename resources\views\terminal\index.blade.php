@extends('layouts.base')
@section('content')
<div class="layout-px-spacing">

    <div class="middle-content container-xxl p-0">

        <div class="row layout-spacing layout-top-spacing" id="cancel-row">
            <div class="col-lg-12">
                <div class="widget-content searchable-container list">

                    <div class="row">
                        <div class="col-xl-4 col-lg-5 col-md-5 col-sm-7 filtered-list-search layout-spacing align-self-center">
                            <form class="form-inline my-2 my-lg-0">
                                <div class="">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-search">
                                        <circle cx="11" cy="11" r="8"></circle>
                                        <line x1="21" y1="21" x2="16.65" y2="16.65"></line>
                                    </svg>
                                    <input type="text" class="form-control product-search" id="input-search" placeholder="Rechercher terminal...">
                                </div>
                            </form>
                        </div>

                        <div class="col-xl-8 col-lg-7 col-md-7 col-sm-5 text-sm-right text-center layout-spacing align-self-center">
                            <div class="d-flex justify-content-sm-end justify-content-center">
                                <a href="{{ route('terminal.create')}}" class="btn btn-primary btn-rounded mb-2 me-4">
                                    Ajouter
                                </a>
                                <div class="switch align-self-center">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-list view-list active-view">
                                        <line x1="8" y1="6" x2="21" y2="6"></line>
                                        <line x1="8" y1="12" x2="21" y2="12"></line>
                                        <line x1="8" y1="18" x2="21" y2="18"></line>
                                        <line x1="3" y1="6" x2="3" y2="6"></line>
                                        <line x1="3" y1="12" x2="3" y2="12"></line>
                                        <line x1="3" y1="18" x2="3" y2="18"></line>
                                    </svg>
                                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-grid view-grid">
                                        <rect x="3" y="3" width="7" height="7"></rect>
                                        <rect x="14" y="3" width="7" height="7"></rect>
                                        <rect x="14" y="14" width="7" height="7"></rect>
                                        <rect x="3" y="14" width="7" height="7"></rect>
                                    </svg>
                                </div>
                            </div>


                        </div>
                    </div>

                    <div class="searchable-items list">
                        <div class="items items-header-section">
                            <div class="item-content">
                                <div class="d-inline-flex">
                                    <div class="n-chk align-self-center text-center">
                                        <div class="form-check form-check-primary me-0 mb-0">
                                            <input class="form-check-input inbox-chkbox" id="contact-check-all" type="checkbox">
                                        </div>
                                    </div>
                                    <h4>Nom</h4>
                                </div>
                                <div class="user-email">
                                    <h4>Adresse Mac</h4>
                                </div>
                                <div class="user-email">
                                    <h4>Numero Serie</h4>
                                </div>
                                <div class="user-location">
                                    <h4 style="margin-left: 0;">Model</h4>
                                </div>
                                <div class="user-phone">
                                    <h4 style="margin-left: 3px;">Marque</h4>
                                </div>
                                <div class="user-porte">
                                    <h4>Porte</h4>
                                </div>

                                <div class="action-btn">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-trash-2  delete-multiple">
                                        <polyline points="3 6 5 6 21 6"></polyline>
                                        <path d="M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6m3 0V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2">
                                        </path>
                                        <line x1="10" y1="11" x2="10" y2="17"></line>
                                        <line x1="14" y1="11" x2="14" y2="17"></line>
                                    </svg>
                                </div>
                            </div>
                        </div>
                        @foreach ($terminals as $terminal)
                        <div class="items">
                            <div class="item-content">
                                <a href="{{ route('terminal.edit', $terminal->id)}}">
                                    <div class="user-profile">
                                        <div class="n-chk align-self-center text-center">
                                            <div class="form-check form-check-primary me-3 mb-0">
                                                <input class="form-check-input inbox-chkbox contact-chkbox" type="checkbox">
                                            </div>
                                        </div>
                                        <!-- <i class="fas fa-credit-card fa-2x mt-1 me-1"></i> -->
                                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-book me-2">
                                            <path d="M4 19.5A2.5 2.5 0 0 1 6.5 17H20"></path>
                                            <path d="M6.5 2H20v20H6.5A2.5 2.5 0 0 1 4 19.5v-15A2.5 2.5 0 0 1 6.5 2z">
                                            </path>
                                        </svg>
                                        <div class="user-meta-info me-3">
                                            <p class="user-name" data-name="{{ $terminal->NomTerminal }}">{{
                                                $terminal->NomTerminal }}</p>
                                            @if($terminal->statut_terminal->Flag == 1)
                                            <span data-occupation="{{ $terminal->statut_terminal->NomStatut }}" class="user-work badge badge-primary text-white">{{
                                                $terminal->statut_terminal->NomStatut }}</span>
                                            @elseif($terminal->statut_terminal->Flag == 2)
                                            <span data-occupation="{{ $terminal->statut_terminal->NomStatut }}" class="user-work badge badge-secondary text-white">{{
                                                $terminal->statut_terminal->NomStatut }}</span>
                                            @elseif($terminal->statut_terminal->Flag == 3)
                                            <span data-occupation="{{ $terminal->statut_terminal->NomStatut }}" class="user-work badge badge-danger text-white">{{
                                                $terminal->statut_terminal->NomStatut }}</span>
                                            @elseif($terminal->statut_terminal->Flag == 4)
                                            <span data-occupation="{{ $terminal->statut_terminal->NomStatut }}" class="user-work badge badge-warning text-white">{{
                                                $terminal->statut_terminal->NomStatut }}</span>
                                            @else
                                            <span data-occupation="{{ $terminal->statut_terminal->NomStatut }}" class="user-work badge badge-success text-white">{{
                                                $terminal->statut_terminal->NomStatut }}</span>
                                            @endif
                                        </div>
                                    </div>
                                </a>
                                <div class="user-email">
                                    <p class="info-title">Adresse Mac </p>
                                    <p class="usr-email-addr">{{ $terminal->AdresseMac }}</p>
                                </div>
                                <div class="user-email">
                                    <p class="info-title">Numero Serie </p>
                                    <p class="usr-email-addr">{{ $terminal->NumeroSerie }}</p>
                                </div>
                                <div class="user-location">
                                    <p class="info-title">Model </p>
                                    <p class="usr-location">{{ $terminal->Model }} </p>
                                </div>
                                <div class="user-phone">
                                    <p class="info-title">Marque: </p>
                                    <p class="usr-ph-no">{{ $terminal->marque->NomMarque }}</p>
                                </div>
                                @if($terminal->porte_id != null)
                                <div class="user-porte">
                                    <p class="usr-ph-porte">{{ $terminal->porte->CodePorte }} - ( {{ $terminal->porte->zone->NomZone }})</p>
                                </div>
                                @else
                                <div class="user-porte">
                                    <p class="usr-ph-porte"> - </p>
                                </div>
                                @endif
                                <div class="action-btn">
                                    @if ($terminal->agent_id == null)
                                    <form action="{{ route('terminal.destroy',$terminal->id) }}" method="POST" enctype="multipart/form-data">
                                        @csrf
                                        @method('DELETE')
                                        <button type="submit" class="btn btn-xs btn-outline-danger btn-default show_confirm" data-toggle="tooltip" title='Delete'><i class="fa fa-times-circle"></i></button>
                                    </form>
                                    @endif
                                </div>
                            </div>
                        </div>
                        @endforeach

                    </div>
                    <!-- la pagination  -->
                    <nav class="mt-4" aria-label="Page navigation">
                        <ul class="pagination justify-content-end">
                            {{-- Lien vers la page précédente --}}
                            @if($terminals->previousPageUrl())
                            <li class="page-item">
                                <a class="page-link" href="{{ $terminals->previousPageUrl() }}" aria-label="Précédent">
                                    <span aria-hidden="true">&laquo; Précédent</span>
                                </a>
                            </li>
                            @else
                            <li class="page-item disabled">
                                <span class="page-link" aria-hidden="true">&laquo; Précédent</span>
                            </li>
                            @endif

                            {{-- Numéros de page --}}
                            @foreach ($terminals->links()->elements[0] as $page => $url)
                            <li class="page-item {{ $terminals->currentPage() == $page ? 'active' : '' }}">
                                <a class="page-link" href="{{ $url }}">{{ $page }}</a>
                            </li>
                            @endforeach

                            {{-- Lien vers la page suivante --}}
                            @if($terminals->nextPageUrl())
                            <li class="page-item">
                                <a class="page-link" href="{{ $terminals->nextPageUrl() }}" aria-label="Suivant">
                                    <span aria-hidden="true">Suivant &raquo;</span>
                                </a>
                            </li>
                            @else
                            <li class="page-item disabled">
                                <span class="page-link" aria-hidden="true">Suivant &raquo;</span>
                            </li>
                            @endif
                        </ul>
                    </nav>

                </div>

            </div>
        </div>

    </div>

</div>
@endsection
@section('js')
<script src="https://cdnjs.cloudflare.com/ajax/libs/sweetalert/2.1.0/sweetalert.min.js"></script>
<script type="text/javascript">
    $('.show_confirm').click(function(event) {
        var form = $(this).closest("form");
        var name = $(this).data("name");
        event.preventDefault();
        Swal.fire({
            title: '<span style="color:#d32f2f; font-size:24px; font-weight:bold;">⚠️ Suppression définitive</span>'
            , html: `
        <div style="font-size:16px; line-height:1.6;">
            <p><strong>Êtes-vous certain de vouloir supprimer ce terminal ?</strong></p>
            <p style="color:#b71c1c;">Cette action <u>ne pourra pas être annulée</u> !</p>
        </div>
    `
            , icon: 'error'
            , showCancelButton: true
            , confirmButtonText: '<i class="fas fa-check"></i> Oui, supprimer'
            , cancelButtonText: '<i class="fas fa-times"></i> Annuler'
            , confirmButtonColor: '#e53935'
            , cancelButtonColor: '#9e9e9e'
            , background: '#fff'
            , customClass: {
                popup: 'shadow border border-danger rounded-3 px-4 py-3'
                , confirmButton: 'btn btn-danger px-4 py-2'
                , cancelButton: 'btn btn-outline-secondary px-4 py-2'
            }
            , backdrop: `
        rgba(0,0,0,0.4)
        left top
        no-repeat
    `
        }).then((result) => {
            if (result.isConfirmed) {
                form.submit();
            }
        });
    });

</script>
@endsection
