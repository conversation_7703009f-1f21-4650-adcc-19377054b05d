//Validation des formulaire avec JQUERY
$(document).ready(function () {
    // Initialiser la validation sur le formulaire
    $('#FormValidationVehicule').validate({
        // Spécifier les règles de validation
        rules: {
            Immatriculation: {
                required: true,
            },
            Model: {
                required: true,
                minlength: 3
            },
            Couleur: {
                required: true,
                minlength: 3
            }
        },
        // Messages d'erreur personnalisés
        messages: {
            Immatriculation: {
                required: "Ce champ est requis",
            },
            Model: {
                required: "Ce champ est requis",
                minlength: "Veillez saisir au minimum 3 caractère"
            },
            Couleur: {
                required: "Ce champ est requis",
                minlength: "Veillez saisir au minimum 3 caractère"
            }

        },
        // Options supplémentaires
        highlight: function (element) {
            $(element).addClass('is-invalid');
        },
        unhighlight: function (element) {
            $(element).removeClass('is-invalid');
        },
        errorElement: 'span',
        errorClass: 'text-danger',
    });
})
