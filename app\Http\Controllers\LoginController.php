<?php

namespace App\Http\Controllers;

use Carbon\Carbon;
use App\Models\Carte;
use App\Http\Requests\LoginRequest;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Session;
use MercurySeries\Flashy\Flashy;

class LoginController extends Controller
{
    public function loginView()
    {
        if (Auth::check()) {
            return redirect()->intended(route('dashboard'));
        }
        return view('auth.login');
    }

    public function defLogUser(LoginRequest $request)
    {
        $credentials = $request->only('email', 'password');
        $remember = $request->boolean('remember'); // ✅ façon clean
        if (Auth::attempt($credentials, $remember)) {
            # regenerate a session for user with request object
            $request->session()->regenerate();
            Flashy::success('Bienvenue ' . Auth::user()->Nom);
            // Check if the user has a card and its expiration date is within 3 month
            // Date dans 3 mois
            $troisMoisPlusTard = Carbon::now()->addMonths(3)->toDateString();

            $cartes_a_expirer_dans_3_mois = Carte::whereDate('DateExpiration', $troisMoisPlusTard)->count();

            if ($cartes_a_expirer_dans_3_mois > 0) {
                Flashy::error("Vous avez " . $cartes_a_expirer_dans_3_mois . " cartes qui doivent expiré(s) dans 3 mois, verifiez");
            }
            return redirect()->intended(route('dashboard'));
        }
        return to_route('login')->withErrors([
            'email' => 'Les informations sont invalides, veuillez contacter un administrateur au ',
            'password' => 'Les informations sont invalides, veuillez reessayer ',
        ])->onlyInput(['email', 'password']);
    }

    public function logout(Request $request)
    {
        Auth::logout();
        $request->session()->invalidate(); // invalide la session
        $request->session()->regenerateToken(); // protège contre CSRF
        Session::flush();
        return redirect()->route('login');
    }
}
