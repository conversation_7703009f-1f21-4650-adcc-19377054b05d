<?php

namespace App\Http\Controllers\Api;

use Exception;
use App\Models\Agent;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Hash;
use App\Http\Requests\AgentLoginRequest;
use App\Models\Scan;
use App\Models\Terminal;
use App\Models\UserTokenManager;
use Carbon\Carbon;

class AgentController extends Controller
{
    //  Login API
    /**
     * @OA\Post(
     *     path="/api/loginAgent",
     *     summary="Connexion Agent",
     *     description="Permet à un agent de se connecter avec son pseudo, son mot de passe et l'identifiant Android de son terminal.",
     *     tags={"Agent Auth"},
     *     @OA\RequestBody(
     *         required=true,
     *         @OA\JsonContent(
     *             required={"pseudo", "password", "android_id", "first_connection"},
     *             @OA\Property(property="pseudo", type="string", example="ayoubadiak"),
     *             @OA\Property(property="password", type="string", example="123456789"),
     *             @OA\Property(property="android_id", type="string", example="abc123xyz456"),
     *             @OA\Property(property="first_connection", type="integer", example=0)
     *         )
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Connexion réussie, retourne le token",
     *          @OA\JsonContent(
     *             @OA\Property(property="statut", type="integer", example=200),
     *             @OA\Property(property="agent_id", type="integer", example=1),
     *             @OA\Property(property="token", type="string", example="1|81XIED3beDzuQfTV3mT4xglvuOaN7VK3hldEO3NBlfabd17ba")
     *         )
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Échec de la connexion : identifiants invalides, compte inactif ou terminal non assigné.",
     *         @OA\JsonContent(
     *             @OA\Property(property="statut", type="integer", example=401),
     *             @OA\Property(property="infoMessage", type="string", example="Chers agent Ce terminal ne vous est pas affecté, veillez contacter votre administrateur")
     *         )
     *     ),
     *     @OA\Response(
     *         response=500,
     *         description="Erreur interne du serveur",
     *         @OA\JsonContent(
     *             @OA\Property(property="statut", type="integer", example=500),
     *             @OA\Property(property="error", type="string", example="Détail de l’erreur")
     *         )
     *     )
     * )
     */

    public function loginAgent(AgentLoginRequest $request)
    {
        $credentials = $request->validated();
        $androidId = $credentials['android_id'];
        $isFirstConnection = $credentials['first_connection'] == 0;

        $messageAgent = "Chers agent, ce terminal ne vous est pas affecté. Veuillez contacter votre administrateur.";
        $messageCompteInactif = "Chers agent, votre compte est inactif. Veuillez contacter votre administrateur.";

        try {
            $agent = Agent::where('Pseudo', $credentials['pseudo'])->first();

            if (!$agent || !Hash::check($credentials['password'], $agent->password)) {
                return response()->json(['statut' => 401], 401);
            }

            if (!$agent->terminal_id) {
                return response()->json(['statut' => 401, 'infoMessage' => $messageAgent]);
            }

            $terminal = Terminal::where('agent_id', $agent->id)->first();

            if (!$terminal) {
                return response()->json(['statut' => 401, 'infoMessage' => $messageAgent]);
            }

            // Vérifie le statut actif du compte
            if ($agent->Statut == 0) {
                return response()->json(['statut' => 401, 'infoMessage' => $messageCompteInactif]);
            }

            // Première connexion
            if ($isFirstConnection) {
                if ($terminal->android_id) {
                    if ($terminal->android_id === $androidId) {
                        return generateAgentToken($agent);
                    } else {
                        return response()->json(['statut' => 401, 'infoMessage' => $messageAgent]);
                    }
                } else {
                    $terminal->android_id = $androidId;
                    $terminal->save();
                    return generateAgentToken($agent);
                }
            }

            // Connexion suivante : vérifie que l’agent utilise le bon terminal
            $validTerminal = Terminal::where('agent_id', $agent->id)
                ->where('android_id', $androidId)
                ->first();

            if (!$validTerminal) {
                return response()->json(['statut' => 401, 'infoMessage' => $messageAgent]);
            }

            return generateAgentToken($agent);
        } catch (\Exception $e) {
            return response()->json([
                'statut' => 500,
                'error' => $e->getMessage()
            ], 500);
        }
    }



    //Logout API
    /**
     * @OA\Post(
     *     path="/api/logoutAgent",
     *     summary="Déconnexion Agent",
     *     description="Déconnecte l'agent en invalidant son token ou sa session.",
     *     tags={"Agent Auth"},
     *     security={{"bearerAuth":{}}},
     *     @OA\Response(
     *         response=200,
     *         description="Déconnexion réussie",
     *         @OA\JsonContent(
     *             type="object",
     *             @OA\Property(property="message", type="string", example="Déconnexion réussie.")
     *         )
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Utilisateur non authentifié",
     *         @OA\JsonContent(
     *             type="object",
     *             @OA\Property(property="message", type="string", example="Non authentifié.")
     *         )
     *     )
     * )
     */
    public function logoutAgent(Request $request)
    {
        try {
            $user = $request->user();
            $token = $user->tokens()->first();

            if ($token) {
                UserTokenManager::where('token', $token->token)->delete();
                $user->tokens()->delete();
            }

            return response()->json(['statut' => 200], 200);
        } catch (\Exception $e) {
            return response()->json([
                'statut' => 500,
                'error' => $e->getMessage()
            ], 500);
        }
    }


    //Get Data Agent Auth

    /**
     * @OA\Get(
     *     path="/api/agentAuthData",
     *     summary="Info Agent Connecter",
     *     description="Permet de recuperer les informations concernant l'agent connecté",
     *     tags={"Data Auth"},
     *     security={{"bearerAuth":{}}},
     *     @OA\Response(
     *         response=200,
     *         description="Donnée reussie avec succès",
     *         @OA\JsonContent(
     *             type="object",
     *             @OA\Property(property="Status", type="integer", example="200")
     *         )
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Utilisateur non authentifié",
     *         @OA\JsonContent(
     *             type="object",
     *             @OA\Property(property="message", type="string", example="Agent non authentifié.")
     *         )
     *     ),
     *      @OA\Response(
     *         response=500,
     *         description="Erreur du serveur",
     *         @OA\JsonContent(
     *             type="object",
     *             @OA\Property(property="statut", type="integer", example="500")
     *         )
     *     )
     * )
     */
    public function getAgentAuth(Request $request)
    {
        try {
            $agent = $request->user();
            $scanAgent = Scan::where('agent_id', $agent->id)
                ->select('carte_id', 'Direction', 'DateHeure')
                ->with([
                    'carte',
                    'carte.vehicule',
                    'carte.vehicule.marqueVoiture',
                    'carte.vehicule.type_vehicule'
                ])->latest()->take(10)->get();

            $totalScanJoursEntre = Scan::where('agent_id', $agent->id)->whereDate('DateHeure', Carbon::now())->where('Direction', 'ENTREE')->count(); //Nbre de scan éffectué pendant la journée encours
            $totalScanJoursSortie = Scan::where('agent_id', $agent->id)->whereDate('DateHeure', Carbon::now())->where('Direction', 'SORTIE')->count(); //Nbre de scan éffectué pendant la journée encours
            $totalScanMoisEntre = Scan::where('agent_id', $agent->id)->whereMonth('DateHeure', Carbon::now()->month)->where('Direction', 'ENTREE')->count(); //Nbre de scan éffectué pendant le mois encours
            $totalScanMoisSortie = Scan::where('agent_id', $agent->id)->whereMonth('DateHeure', Carbon::now()->month)->where('Direction', 'SORTIE')->count(); //Nbre de scan éffectué pendant le mois encours
            $totalScanAnneeEntre = Scan::where('agent_id', $agent->id)->whereYear('DateHeure', Carbon::now()->year)->where('Direction', 'ENTREE')->count(); //Nbre de scan éffectué pendant l'année encours
            $totalScanAnneeSortie = Scan::where('agent_id', $agent->id)->whereYear('DateHeure', Carbon::now()->year)->where('Direction', 'SORTIE')->count(); //Nbre de scan éffectué pendant l'année encours

            return response()->json([
                'statut' => 200,
                'agent' => $agent, //info Agent
                'scanInfo' => $scanAgent, //Info Scan
                'totalScanJoursEntre' => $totalScanJoursEntre,
                'totalScanJoursSortie' =>  $totalScanJoursSortie,
                'totalScanMoisEntre' => $totalScanMoisEntre,
                'totalScanMoisSortie' => $totalScanMoisSortie,
                'totalScanAnneeEntre' => $totalScanAnneeEntre,
                'totalScanAnneeSortie' => $totalScanAnneeSortie
            ]);
        } catch (Exception $e) {
            return response()->json([
                'statut' => 500,
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    public function getProfileAgent(Request $request)
    {
        return response()->jsont([
            'statut' => 200,
            'agent' => $request->user()
        ]);
    }
}
