<?php

namespace App\Http\Controllers;

use App\Models\Beneficiaire;
use App\Models\CardPressoInfoCard;
use App\Models\Carte;
use App\Models\Parametre;
use App\Models\Porte;
use App\Models\Vehicule;
use App\Models\Zone;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Crypt;
use MercurySeries\Flashy\Flashy;

class CarteController extends Controller
{
    //Fonction d'affichage des cartes
    public function index()
    {
        $cartes = Carte::all();
        return view('carte.index', [
            'cartes' => $cartes,
        ]);
    }

    // request to get all vehicules from one benficiaire
    public function getVehicules(Request $request)
    {
        $beneficiaireId = $request->input('beneficiaire');

        if (!$beneficiaireId) {
            return redirect()->route('carte.create');
        }

        $beneficiaire = Beneficiaire::find($beneficiaireId);
        $vehicules = $beneficiaire ? $beneficiaire->vehicules->where('Statut', 1) : [];
        $Beneficiaires = Beneficiaire::all();
        $zones = Zone::all();

        return view('carte.create', [
            'Beneficiaires' => $Beneficiaires,
            'vehicules' => $vehicules,
            'beneficiaireId' => $beneficiaireId,
            'zones' => $zones,
            'NumeroAutorisation' => $request->input('NumeroAutorisation'),
            'DateExpiration' => $request->input('DateExpiration')
        ]);
    }

    //Fonction de creation d'une carte
    public function create()
    {
        $parms = Parametre::all()->count();
        $zones = Zone::all();
        $portes = Porte::all();
        $vehicules = Vehicule::all();
        if ($parms == 0) {
            flashy()->error("Veuillez paramétrer la durée de validité des cartes");
            return redirect()->route('carte.index');
        } else {
            $NumeroAutorisation = generateNumeroAutorisation();
            $Beneficiaires = Beneficiaire::all();

            return view('carte.create', [
                'NumeroAutorisation' => $NumeroAutorisation,
                'Beneficiaires' => $Beneficiaires,
                'zones' => $zones,
                'portes' => $portes,
                'vehicules' => $vehicules
            ]);
        }
    }

    // Fonction d'enregistrement d'une carte
    public function store(Request $request)
    {
        $user_id = auth()->id(); // plus court
        $vehicule = Vehicule::findOrFail($request->vehicule_id);

        if ($vehicule->Statut == 0) {
            Flashy::error("Impossible !! Le statut du véhicule est désactivé");
            return redirect()->route('carte.create');
        }

        $carteExistante = Carte::where('vehicule_id', $vehicule->id)
            ->exists();

        if ($carteExistante) {
            Flashy::error("Impossible !! Le véhicule " . $vehicule->Immatriculation . " a déjà une carte en cours");
            return redirect()->route('carte.create');
        }

        if ($request->portes != null) {
            Carte::create([
                'NumeroAutorisation' => $request['NumeroAutorisation'],
                'vehicule_id' => $vehicule->id,
                'Commentaire' => $request->Commentaire,
                'Portes' => json_encode($request->portes, JSON_UNESCAPED_UNICODE),
                'CodeQR' => Crypt::encryptString($vehicule->Immatriculation),
                'user_id' => $user_id
            ]);
        } else {
            Flashy::error("Impossible !! Aucune porte n'a été sélectionnée");
            return redirect()->route('carte.create');
        }

        Flashy::primary("Carte générée avec succès");
        return redirect()->route('carte.index');
    }


    public function show($id)
    {
        $carte = Carte::find($id);
        return view('carte.show', compact('carte'));
    }

    public function edit(string $id)
    {
        $carte = Carte::find($id);
        $zones = Zone::all();
        $portes = Porte::all();
        return view('carte.edit', [
            'carte' => $carte,
            'zones' => $zones,
            'porte' => $portes
        ]);
    }

    public function update(Request $request, string $id)
    {
        $carte = Carte::findOrFail($id);
        $carte->Portes = json_encode($request->portes, JSON_UNESCAPED_UNICODE);
        $carte->save();

        // Charger les portes avec leur zone
        $porteIds = $request->portes;

        if ($porteIds != null) {
            $zoneAccessList = Porte::whereIn('id', $porteIds)
                ->with('zone')
                ->get()
                ->map(function ($porte) {
                    return $porte->zone ? "{$porte->CodePorte}({$porte->zone->NomZone})" : null;
                })
                ->filter()
                ->toArray();

            $zoneAccess = implode(', ', $zoneAccessList);

            // Mettre à jour CardPressoInfoCard lié à cette carte
            $cardPressoInfoCard = CardPressoInfoCard::where('NumeroAutorisationCarte', $carte->NumeroAutorisation)->first();

            if ($cardPressoInfoCard != null) {
                $cardPressoInfoCard->update(
                    [
                        'NumeroAutorisationCarte' => $carte->NumeroAutorisation,
                        'NomBeneficiaire' => $carte->vehicule->beneficiaire->RaisonSociale,
                        'NumeroImmatriculationVehicule' => $carte->vehicule->Immatriculation,
                        'ZoneAccess' => $zoneAccess,
                    ]
                );
            }
        }

        Flashy::success("Accès mis à jour avec succès");
        return redirect()->route('carte.show', $carte->id);
    }


    //Fonction d'annulation d'une carte
    public function destroy($id)
    {
        Carte::find($id)->delete();
        Flashy::warning("Annulation de la carte éffectuée succès");
        return redirect()->back();
    }

    //Operation de reactivation d'un carte
    public function reactivation_carte()
    {
        $cartes = Carte::where('Statut', 0)->get();

        return view('reactivation.index', [
            'cartes' => $cartes,
        ]);
    }

    public function reactivate_card($id)
    {
        $carte = Carte::find($id);
        // Vérifier s'il existe une autre carte active pour le même véhicule
        $today = Carbon::now();
        $query = Carte::query();
        $carteActiveExist = $query->where('vehicule_id', $carte->vehicule_id)
            ->where('id', '<>', $carte->id)->where('Statut', 1)->count();
        $carteDateExpirationDepasse = Carte::where('id', $id)->whereDate('DateExpiration', $today)->exists();
        if ($carteActiveExist > 0) {
            Flashy::error("Le véhicule " . $carte->vehicule->Immatriculation . " a déjà une carte active");
            return redirect()->back();
        } elseif ($carteDateExpirationDepasse) {
            Flashy::error("La date d'expiration du véhicule " . $carte->vehicule->Immatriculation . " est arrivé");
            return redirect()->back();
        } else {
            $carte->update(array('Statut' => 1));
            Flashy::primary("Activation de la carte éffectuée avec succès");
            return redirect()->back();
        }
    }

    //Operation de DESACTIVATION DE LA CARTE
    public function desactivation_carte(Request $request)
    {
        // Appliquer la pagination
        $cartes = Carte::where('Statut', 1)->get();

        return view('desactivation.index', [
            'cartes' => $cartes,
        ]);
    }

    // function pour recuperer l'id de la carte qui doit etre desactive
    public function desactiverCarte($id)
    {
        // recuperer l'id de la carte qui doit etre desactiver
        $carte = Carte::findOrFail($id);
        return view('desactivation.addComment', ['id' => $id, 'carte' => $carte]);
    }

    function desactivate_card(Request $request, $id)
    {
        Carte::where('id', $id)->update(array(
            'Commentaire_desactivation' => $request->Commentaire_desactivation,
            'Statut' => 0,
        ));
        Flashy::warning("Désactivation de la carte éffectuée avec succès");
        return redirect()->route('desactivation');
    }

    //Operation D'approbation
    public function approbation_carte()
    {
        // Appliquer la pagination
        $cartes = Carte::all();

        return view('approbation.index', [
            'cartes' => $cartes,
        ]);
    }

    public function change_state_approved($id)
    {
        $carte = Carte::findOrFail($id);
        $vehicule = $carte->vehicule;

        // Vérifie s'il existe une autre carte active pour ce véhicule
        $existe = Carte::where('vehicule_id', $vehicule->id)
            ->where('id', '!=', $carte->id)
            ->where('StatutApprobation', 1)
            ->exists();

        if ($existe) {
            Flashy::error("Approbation Impossible !! Le véhicule {$vehicule->Immatriculation} a déjà une carte activée");
            return back();
        }

        $porteIds = $carte->Portes ? json_decode($carte->Portes, true) : [];
        $now = Carbon::now();
        $expiration = $now->copy()
            ->addDays(Parametre::first()->NombreJoursExpiration)
            ->subDay(); // soustrait 1 jour

        // Si la carte est approuvée, on désapprouve
        if ($carte->StatutApprobation === 1) {
            $carte->update([
                'StatutApprobation' => 0,
                'Statut' => 0
            ]);

            //Suppression de la carte dans la table cardpresso quand on désapprouve
            CardPressoInfoCard::where('NumeroAutorisationCarte', $carte->NumeroAutorisation)
                ->first()
                ->delete();

            Flashy::warning("Désapprobation de la carte éffectuée avec succès");
            return back();
        }

        // Sinon on approuve ou réapprouve
        $carte->update([
            'StatutApprobation' => 1,
            'Statut' => 0,
            'DateApprobation' => $now,
            'DateExpiration' => $expiration
        ]);

        // Préparer la zone d'accès
        $zoneAccess = 'Pas d\'accès, par défaut le véhicule n\'a accès à aucune porte';
        if (!empty($porteIds)) {
            $zoneAccessList = Porte::whereIn('id', $porteIds)
                ->with('zone')
                ->get()
                ->map(function ($porte) {
                    return $porte->zone ? "{$porte->CodePorte} ({$porte->zone->NomZone})" : null;
                })
                ->filter()
                ->toArray();

            if ($zoneAccessList) {
                $zoneAccess = implode(', ', $zoneAccessList);
            }
        }

        // Création dans CardPresso
        CardPressoInfoCard::create([
            'NumeroAutorisationCarte' => $carte->NumeroAutorisation,
            'NomBeneficiaire' => $vehicule->beneficiaire->RaisonSociale,
            'NumeroImmatriculationVehicule' => $vehicule->Immatriculation,
            'DateApprobation' => $now->format('d/m/Y'),
            'DateExpiration' => $expiration->format('d/m/Y'),
            'ZoneAccess' => $zoneAccess,
            'QrCode' => $carte->CodeQR,
        ]);

        Flashy::success("Approbation de la carte éffectuée avec succès");
        return back();
    }
}
