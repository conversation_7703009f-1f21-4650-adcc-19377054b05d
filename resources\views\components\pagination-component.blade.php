{{-- Pagination personnalisée --}}
<nav aria-label="Page navigation example">
    <ul class="pagination justify-content-center">
        <!-- Lien vers la page précédente -->
        @if($variablePaginated->onFirstPage())
        <li class="page-item disabled">
            <span class="page-link" tabindex="-1">Previous</span>
        </li>
        @else
        <li class="page-item">
            <a class="page-link" href="{{ $variablePaginated->previousPageUrl() }}" tabindex="-1">Previous</a>
        </li>
        @endif

        <!-- Liens vers les numéros de page -->
        @for($i = 1; $i <= $variablePaginated->lastPage(); $i++)
            <li class="page-item {{ $i == $variablePaginated->currentPage() ? 'active' : '' }}">
                <a class="page-link" href="{{ $variablePaginated->url($i) }}">{{ $i }}</a>
            </li>
            @endfor

            <!-- Lien vers la page suivante -->
            @if($variablePaginated->hasMorePages())
            <li class="page-item">
                <a class="page-link" href="{{ $variablePaginated->nextPageUrl() }}">Next</a>
            </li>
            @else
            <li class="page-item disabled">
                <span class="page-link">Next</span>
            </li>
            @endif
    </ul>
</nav>
