<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('terminals', function (Blueprint $table) {
            $table->id();
            $table->string('NomTerminal');
            $table->string('AdresseMac')->unique();
            $table->string('NumeroSerie')->unique();
            $table->string('android_id')->unique()->nullable();
            $table->string('Description')->nullable();
            $table->string('Model');
            $table->text('Commentaire')->nullable();
            $table->unsignedBigInteger('marque_id')->index();
            $table->foreign('marque_id')->references('id')->on('marques')->onDelete('cascade')->onUpdate('cascade');
            $table->unsignedBigInteger('statut_terminal_id')->index();
            $table->foreign('statut_terminal_id')->references('id')->on('statut_terminals')->onDelete('cascade')->onUpdate('cascade');
            $table->unsignedBigInteger('agent_id')->nullable();
            $table->foreign('agent_id')->references('id')->on('agents')->onDelete('cascade')->onUpdate('cascade');
            $table->unsignedBigInteger('user_id')->index();
            $table->foreign('user_id')->references('id')->on('users')->onDelete('cascade')->onUpdate('cascade');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('terminals');
    }
};
