<?php

namespace App\Http\Controllers;

use App\Models\Parametre;
use Illuminate\Http\Request;
use MercurySeries\Flashy\Flashy;

class ParametreController extends Controller
{
    public function index()
    {
        $NombreJoursExpiration = Parametre::all();
        return view('parametre.index', compact('NombreJoursExpiration'));
    }

    public function store(Request $request)
    {
        //NombreAnnee
        $NombreJoursExpiration = Parametre::all();
        if ($NombreJoursExpiration->count() > 0) {
            Flashy::error('Attention !! vous avez déjà enregistrer un nombre, veuillez modifier ou supprimer avant de créer un nouveau ');
            return redirect()->back();
        } else {
            $this->validate($request, [
                'NombreJoursExpiration' => 'required|numeric',
            ]);
            Parametre::create([
                'NombreJoursExpiration' => $request->NombreJoursExpiration,
                'user_id' => auth()->user()->parent_id
            ]);
            Flashy::primary("Paramètre enregistré avec succès");
            return redirect()->back();
        }
    }

    public function edit(string $id)
    {
        $parametre = Parametre::find($id);
        return view('parametre.edit', compact('parametre'));
    }

    public function update(Request $request, string $id)
    {
        $this->validate($request, [
            'NombreJoursExpiration' => 'required|numeric',
        ]);

        $parametre = Parametre::find($id);
        $parametre->NombreJoursExpiration = $request->NombreJoursExpiration;
        $parametre->save();

        Flashy::success("Paramètre modifié avec succès");
        return redirect()->route('parametre.index');
    }

    public function destroy(string $id)
    {
        Parametre::find($id)->delete();
        Flashy::warning("Paramètre supprimé avec succès");
        return back();
    }
}
