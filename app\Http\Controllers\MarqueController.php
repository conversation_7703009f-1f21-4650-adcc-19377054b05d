<?php

namespace App\Http\Controllers;

use App\Models\Marque;
use App\Models\Terminal;
use Illuminate\Http\Request;
use MercurySeries\Flashy\Flashy;

class MarqueController extends Controller
{
    public function index()
    {
        $marques = Marque::all();
        $marquesCount = Marque::whereNull('created_at')->count();
        return view('marque.index', compact('marques', 'marquesCount'));
    }

    public function store(Request $request)
    {
        $this->validate($request, [
            'NomMarque' => 'required|min:2',

        ]);

        Marque::create([
            'NomMarque' => $request->NomMarque,
            'user_id' => auth()->user()->id
        ]);
        Flashy::primary("Marque terminal enregistrée avec succès");
        return redirect()->route('marque.index');
    }

    public function edit(string $id)
    {
        $marque = Marque::find($id);
        return view('marque.edit', compact('marque'));
    }

    public function update(Request $request, $id)
    {
        $this->validate($request, [
            'NomMarque' => 'required|min:2',
        ]);

        $marqueTerminaux = Marque::find($id);
        $marqueTerminaux->NomMarque = $request->NomMarque;
        $marqueTerminaux->save();

        Flashy::success("Marque terminal modifié avec succès");
        return redirect()->route('marque.index');
    }

    public function destroy(string $id, Request $request)
    {
        $terminal = Terminal::where('marque_id', $id)->get();
        if ($terminal->count() == 0) {
            Marque::find($id)->delete();
            Flashy::warning("Marque terminal supprimer avec succès");
            return back();
        } else {

            Flashy::error("Impossible, Marque terminal déjà utilisé");
            return back();
        }
    }
}
