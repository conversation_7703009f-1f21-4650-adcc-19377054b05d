<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;
use Flashy;

class CheckStatus
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        if (auth()->user()->Statut == 0){
            Flashy::error('Votre compte est bloqué, contactez un administrateur de la plateforme');
            auth()->logout();
            return back();
            // to_route('home')
        }
        return $next($request);
    }
}
