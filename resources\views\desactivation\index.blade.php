@extends('layouts.base')
@section('content')
<div class="layout-px-spacing">
    <div class="middle-content container-xxl p-0">
        <div id="tableCustomBasic" class="col-lg-12 col-12 layout-spacing">
            <div class="d-flex justify-content-between align-items-center flex-wrap mb-4 mt-5 p-3 rounded shadow-sm bg-white border">
                <h2 class="fw-bold mb-0 text-dark">Liste des cartes à désactiver</h2>
            </div>
            <div class="statbox widget box box-shadow">
                <div class="widget-content widget-content-area">
                    <table id="zero-config" class="table style-1 dt-table-hover non-hover">
                        <thead>
                            <tr>
                                <th class="" scope="col">Numero carte</th>
                                <th class="text-center" scope="col">Immat Véhicule</th>
                                <th class="text-center" scope="col">Date Expiration</th>
                                <th class="text-center" scope="col">Bénéficiaire</th>
                                <th class="text-center" scope="col">Statut Carte</th>
                                <th class="text-center" scope="col">Action</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($cartes as $carte)
                            <tr>
                                <td class=""><a href="{{ route('carte.show', $carte->id) }}">{{ $carte->NumeroAutorisation}}</a></td>
                                <td class="text-center">{{ $carte->vehicule->Immatriculation}}</td>
                                <td class="text-center">@if($carte->DateExpiration)
                                    {{ \Carbon\Carbon::parse($carte->DateExpiration)->format('d-m-Y') }}@else null
                                    @endif</td>
                                <td class="text-center">{{ $carte->vehicule->beneficiaire->RaisonSociale}}</td>
                                <td class="text-center">
                                    <span class="badge badge-light-success bg-opacity-15 px-2 py-6px rounded fs-12px d-inline-flex align-items-center"><i class="fa fa-circle fs-9px fa-fw me-5px"></i>Activé</span>
                                </td>
                                <td class="text-center">
                                    <form action="{{ route('desactiver-carte', $carte->id) }}" method="POST" id="submit{{ $carte->id}}">
                                        @csrf
                                        <input type="hidden" name="carte_id" value="{{ $carte->id }}">
                                        <button type="submit" class="btn btn-xs btn-outline-warning show_confirm_carte" data-toggle="tooltip" title='Désactiver la carte'> <i class="fa fa-ban"></i></button>
                                    </form>
                                </td>
                            </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
@section('js')
<script src="https://cdnjs.cloudflare.com/ajax/libs/sweetalert/2.1.0/sweetalert.min.js"></script>
<script type="text/javascript">
    $('.show_confirm_carte').click(function(event) {
        var form = $(this).closest("form");
        var name = $(this).data("name");
        var carteId = form.find('input[name="carte_id"]').val(); // Récupérer l'ID du terminal du formulaire
        event.preventDefault();
        Swal.fire({
            title: 'Désactivation carte'
            , html: `<strong style="color:#e2a03f">Vous êtes sur le point de désactiver la carte.</strong>`
            , icon: 'question'
            , showCancelButton: true
            , confirmButtonText: 'Oui, désactiver'
            , cancelButtonText: 'Annuler'
            , confirmButtonColor: '#f79102'
            , cancelButtonColor: '#87adbd'
            , customClass: {
                popup: 'shadow-lg rounded-lg'
                , confirmButton: 'btn btn-warning'
                , cancelButton: 'btn btn-secondary me-2'
            }
        }).then((result) => {
            if (result.isConfirmed) {
                form.submit();
            }
        });
    });

</script>
@endsection
